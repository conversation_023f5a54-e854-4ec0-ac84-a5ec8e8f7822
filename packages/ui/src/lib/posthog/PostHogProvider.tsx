'use client';

import { UserWithPreferenceSpaceDto } from '@repo/api/generated-client/snake-case/index';
import posthog from 'posthog-js';
import { PostHogProvider as PHProvider, usePostHog } from 'posthog-js/react';
import { Suspense, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { flattenObject } from './object';

export function PostHogProvider({
  children,
  user,
}: {
  children: React.ReactNode;
  user: UserWithPreferenceSpaceDto;
}) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    if (!user?.id) return;
    setIsMounted(true);
    posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
      api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
      persistence: 'localStorage',
      capture_pageview: false, // We capture pageviews manually
      capture_pageleave: true, // Enable pageleave capture
      debug: false, // 关闭调试模式以禁止控制台日志
      autocapture: false, // Disable automatic event capture
      enable_recording_console_log: false, // 禁用控制台日志记录
      disable_session_recording: true, // 禁用会话录制
      // 启用 Web Vitals 等性能指标上报
      capture_performance: true, // 启用性能指标收集
      loaded: (phInstance) => {
        const distinct_id = user?.id;
        const userProperties = flattenObject(user);
        if (distinct_id) {
          phInstance.identify(distinct_id, userProperties);
        }
      },
    });
  }, [user?.id, user]);

  // 只在客户端渲染 PostHog 相关内容
  if (!isMounted) {
    return <>{children}</>;
  }

  return (
    <PHProvider client={posthog}>
      <SuspendedPostHogPageView />
      {children}
    </PHProvider>
  );
}

function PostHogPageView() {
  const location = useLocation();
  const posthog = usePostHog();

  useEffect(() => {
    // 如果 pathname 或 posthog 实例不存在，则不执行任何操作
    if (!location.pathname || !posthog || typeof window === 'undefined') {
      return;
    }

    // 设置一个2秒的定时器
    const timerId = setTimeout(() => {
      let url = window.origin + location.pathname;
      if (location.search) {
        url += location.search;
      }
      posthog.capture('$pageview', { $current_url: url });
    }, 2000); // 2秒延迟

    // 清理函数：当 pathname, search, 或 posthog 变化时，
    // 或者组件卸载时，清除定时器
    return () => {
      clearTimeout(timerId);
    };
  }, [location.pathname, location.search, posthog]);

  return null;
}

function SuspendedPostHogPageView() {
  return (
    <Suspense fallback={null}>
      <PostHogPageView />
    </Suspense>
  );
}
