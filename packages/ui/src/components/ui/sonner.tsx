'use client';

import { X } from 'lucide-react';
import { Toaster as Sonner, toast as sonnerToast, ToasterProps } from 'sonner';
import { Button } from './button';

export const Toaster = ({ ...props }: ToasterProps) => {
  return <Sonner position="top-center" className="toaster group" {...props} />;
};

type Position =
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right'
  | 'top-center'
  | 'bottom-center';

export type titleT = (() => React.ReactNode) | React.ReactNode;

export interface ToastConfig {
  duration?: number;
  label?: string;
  onClick?: () => void;
  position?: Position;
}

export const dismissToast = (id: string | number) => {
  sonnerToast.dismiss(id);
};

export { sonnerToast };

export const toast = (message: string, config?: ToastConfig) => {
  const { label, onClick, position } = config || {};
  return sonnerToast.custom(
    (id) => {
      return (
        <div className="px-4 py-3 bg-card rounded-lg shadow-lg flex items-center gap-x-4" key={id}>
          <div className="text-sm">{message}</div>
          {label && (
            <Button variant="outline" size="sm" onClick={onClick}>
              {label}
            </Button>
          )}
          <X
            className="cursor-pointer flex-shrink-0 min-w-[14px]"
            size={14}
            onClick={() => sonnerToast.dismiss(id)}
          />
        </div>
      );
    },
    {
      duration: config?.duration || 3000,
      position,
    },
  );
};
