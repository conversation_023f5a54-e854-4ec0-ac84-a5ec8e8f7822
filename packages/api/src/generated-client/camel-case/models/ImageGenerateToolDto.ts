/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface ImageGenerateToolDto
 */
export interface ImageGenerateToolDto {
  /**
   * Tool use option
   * @type {string}
   * @memberof ImageGenerateToolDto
   */
  useTool: ImageGenerateToolDtoUseToolEnum;
  /**
   * Image size
   * @type {string}
   * @memberof ImageGenerateToolDto
   */
  size?: ImageGenerateToolDtoSizeEnum;
  /**
   * Image style
   * @type {string}
   * @memberof ImageGenerateToolDto
   */
  style?: ImageGenerateToolDtoStyleEnum;
  /**
   * Image quality
   * @type {string}
   * @memberof ImageGenerateToolDto
   */
  quality?: ImageGenerateToolDtoQualityEnum;
}

/**
 * @export
 * @enum {string}
 */
export enum ImageGenerateToolDtoUseToolEnum {
  required = 'required',
  auto = 'auto',
  none = 'none',
  unknownDefaultOpenApi = '11184809',
}
/**
 * @export
 * @enum {string}
 */
export enum ImageGenerateToolDtoSizeEnum {
  square = 'square',
  portrait = 'portrait',
  landscape = 'landscape',
  unknownDefaultOpenApi = '11184809',
}
/**
 * @export
 * @enum {string}
 */
export enum ImageGenerateToolDtoStyleEnum {
  ghibili = 'ghibili',
  pixar = 'pixar',
  cartoon = 'cartoon',
  pixel = 'pixel',
  unknownDefaultOpenApi = '11184809',
}
/**
 * @export
 * @enum {string}
 */
export enum ImageGenerateToolDtoQualityEnum {
  low = 'low',
  medium = 'medium',
  high = 'high',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the ImageGenerateToolDto interface.
 */
export function instanceOfImageGenerateToolDto(value: object): value is ImageGenerateToolDto {
  if (!('useTool' in value) || value.useTool === undefined) return false;
  return true;
}

export function ImageGenerateToolDtoFromJSON(json: any): ImageGenerateToolDto {
  return ImageGenerateToolDtoFromJSONTyped(json, false);
}

export function ImageGenerateToolDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ImageGenerateToolDto {
  if (json == null) {
    return json;
  }
  return {
    useTool: json.useTool,
    size: json.size == null ? undefined : json.size,
    style: json.style == null ? undefined : json.style,
    quality: json.quality == null ? undefined : json.quality,
  };
}

export function ImageGenerateToolDtoToJSON(json: any): ImageGenerateToolDto {
  return ImageGenerateToolDtoToJSONTyped(json, false);
}

export function ImageGenerateToolDtoToJSONTyped(
  value?: ImageGenerateToolDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    useTool: value.useTool,
    size: value.size,
    style: value.style,
    quality: value.quality,
  };
}
