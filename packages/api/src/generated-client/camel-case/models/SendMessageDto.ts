/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { AtReferenceDto } from './AtReferenceDto';
import {
  AtReferenceDtoFromJSON,
  AtReferenceDtoFromJSONTyped,
  AtReferenceDtoToJSON,
  AtReferenceDtoToJSONTyped,
} from './AtReferenceDto';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  ChatOriginDtoToJSON,
  ChatOriginDtoToJSONTyped,
} from './ChatOriginDto';
import type { EditCommandDto } from './EditCommandDto';
import {
  EditCommandDtoFromJSON,
  EditCommandDtoFromJSONTyped,
  EditCommandDtoToJSON,
  EditCommandDtoToJSONTyped,
} from './EditCommandDto';
import type { ShortcutDto } from './ShortcutDto';
import {
  ShortcutDtoFromJSON,
  ShortcutDtoFromJSONTyped,
  ShortcutDtoToJSON,
  ShortcutDtoToJSONTyped,
} from './ShortcutDto';
import type { UseToolsDto } from './UseToolsDto';
import {
  UseToolsDtoFromJSON,
  UseToolsDtoFromJSONTyped,
  UseToolsDtoToJSON,
  UseToolsDtoToJSONTyped,
} from './UseToolsDto';

/**
 *
 * @export
 * @interface SendMessageDto
 */
export interface SendMessageDto {
  /**
   * Message content
   * @type {string}
   * @memberof SendMessageDto
   */
  message: string;
  /**
   * Board Id
   * @type {string}
   * @memberof SendMessageDto
   */
  boardId?: string;
  /**
   * Selected text context
   * @type {string}
   * @memberof SendMessageDto
   */
  selection?: string;
  /**
   * Referenced entities
   * @type {Array<AtReferenceDto>}
   * @memberof SendMessageDto
   */
  atReferences?: Array<AtReferenceDto>;
  /**
   * AI model to use
   * @type {string}
   * @memberof SendMessageDto
   */
  chatModel?: SendMessageDtoChatModelEnum;
  /**
   * Tools configuration
   * @type {Array<UseToolsDto>}
   * @memberof SendMessageDto
   */
  tools?: Array<UseToolsDto>;
  /**
   * Shortcut information
   * @type {Array<ShortcutDto>}
   * @memberof SendMessageDto
   */
  shortcut?: Array<ShortcutDto>;
  /**
   * Message mode
   * @type {string}
   * @memberof SendMessageDto
   */
  messageMode?: SendMessageDtoMessageModeEnum;
  /**
   * Edit command
   * @type {Array<EditCommandDto>}
   * @memberof SendMessageDto
   */
  command?: Array<EditCommandDto>;
  /**
   * Chat origin context
   * @type {ChatOriginDto}
   * @memberof SendMessageDto
   */
  origin?: ChatOriginDto;
  /**
   * Chat ID to send message to
   * @type {string}
   * @memberof SendMessageDto
   */
  chatId: string;
}

/**
 * @export
 * @enum {string}
 */
export enum SendMessageDtoChatModelEnum {
  gpt4o = 'gpt-4o',
  gpt4oMini = 'gpt-4o-mini',
  gpt41 = 'gpt-4.1',
  gpt41Mini = 'gpt-4.1-mini',
  gpt41Nano = 'gpt-4.1-nano',
  o1Mini = 'o1-mini',
  o1Preview = 'o1-preview',
  o3Mini = 'o3-mini',
  o4Mini = 'o4-mini',
  textEmbedding3Large = 'text-embedding-3-large',
  tts1 = 'tts-1',
  tts1Hd = 'tts-1-hd',
  gpt4oMiniTts = 'gpt-4o-mini-tts',
  gptImage1 = 'gpt-image-1',
  claude4Sonnet = 'claude-4-sonnet',
  claude37Sonnet = 'claude-3-7-sonnet',
  claude37SonnetThinking = 'claude-3-7-sonnet-thinking',
  claude35Sonnet = 'claude-3-5-sonnet',
  claude35Haiku = 'claude-3-5-haiku',
  deepseekDeepseekV3 = 'deepseek/deepseek_v3',
  deepseekDeepseekR1 = 'deepseek/deepseek-r1',
  gemini25Pro = 'gemini-2.5-pro',
  gemini25Flash = 'gemini-2.5-flash',
  gemini25FlashLite = 'gemini-2.5-flash-lite',
  gemini20Flash = 'gemini-2.0-flash',
  gemini20FlashLite = 'gemini-2.0-flash-lite',
  qwenPlus = 'qwen-plus',
  qwenMax = 'qwen-max',
  qwenTurbo = 'qwen-turbo',
  speech02Hd = 'speech-02-hd',
  speech02Turbo = 'speech-02-turbo',
  speech01Hd = 'speech-01-hd',
  speech01Turbo = 'speech-01-turbo',
  unknownDefaultOpenApi = '11184809',
}
/**
 * @export
 * @enum {string}
 */
export enum SendMessageDtoMessageModeEnum {
  ask = 'ask',
  agent = 'agent',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the SendMessageDto interface.
 */
export function instanceOfSendMessageDto(value: object): value is SendMessageDto {
  if (!('message' in value) || value.message === undefined) return false;
  if (!('chatId' in value) || value.chatId === undefined) return false;
  return true;
}

export function SendMessageDtoFromJSON(json: any): SendMessageDto {
  return SendMessageDtoFromJSONTyped(json, false);
}

export function SendMessageDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): SendMessageDto {
  if (json == null) {
    return json;
  }
  return {
    message: json.message,
    boardId: json.boardId == null ? undefined : json.boardId,
    selection: json.selection == null ? undefined : json.selection,
    atReferences:
      json.atReferences == null
        ? undefined
        : (json.atReferences as Array<any>).map(AtReferenceDtoFromJSON),
    chatModel: json.chatModel == null ? undefined : json.chatModel,
    tools: json.tools == null ? undefined : (json.tools as Array<any>).map(UseToolsDtoFromJSON),
    shortcut:
      json.shortcut == null ? undefined : (json.shortcut as Array<any>).map(ShortcutDtoFromJSON),
    messageMode: json.messageMode == null ? undefined : json.messageMode,
    command:
      json.command == null ? undefined : (json.command as Array<any>).map(EditCommandDtoFromJSON),
    origin: json.origin == null ? undefined : ChatOriginDtoFromJSON(json.origin),
    chatId: json.chatId,
  };
}

export function SendMessageDtoToJSON(json: any): SendMessageDto {
  return SendMessageDtoToJSONTyped(json, false);
}

export function SendMessageDtoToJSONTyped(
  value?: SendMessageDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    message: value.message,
    boardId: value.boardId,
    selection: value.selection,
    atReferences:
      value.atReferences == null
        ? undefined
        : (value.atReferences as Array<any>).map(AtReferenceDtoToJSON),
    chatModel: value.chatModel,
    tools: value.tools == null ? undefined : (value.tools as Array<any>).map(UseToolsDtoToJSON),
    shortcut:
      value.shortcut == null ? undefined : (value.shortcut as Array<any>).map(ShortcutDtoToJSON),
    messageMode: value.messageMode,
    command:
      value.command == null ? undefined : (value.command as Array<any>).map(EditCommandDtoToJSON),
    origin: ChatOriginDtoToJSON(value.origin),
    chatId: value.chatId,
  };
}
