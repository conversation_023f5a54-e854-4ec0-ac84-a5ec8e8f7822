/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { StreamDataTypeEnum } from './StreamDataTypeEnum';
import {
  StreamDataTypeEnumFromJSON,
  StreamDataTypeEnumFromJSONTyped,
  StreamDataTypeEnumToJSON,
  StreamDataTypeEnumToJSONTyped,
} from './StreamDataTypeEnum';

/**
 *
 * @export
 * @interface StreamTraceDto
 */
export interface StreamTraceDto {
  /**
   * The type of stream message
   * @type {StreamDataTypeEnum}
   * @memberof StreamTraceDto
   */
  type: StreamDataTypeEnum;
  /**
   * The trace ID for request tracking
   * @type {string}
   * @memberof StreamTraceDto
   */
  traceId: string;
}

/**
 * Check if a given object implements the StreamTraceDto interface.
 */
export function instanceOfStreamTraceDto(value: object): value is StreamTraceDto {
  if (!('type' in value) || value.type === undefined) return false;
  if (!('traceId' in value) || value.traceId === undefined) return false;
  return true;
}

export function StreamTraceDtoFromJSON(json: any): StreamTraceDto {
  return StreamTraceDtoFromJSONTyped(json, false);
}

export function StreamTraceDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamTraceDto {
  if (json == null) {
    return json;
  }
  return {
    type: StreamDataTypeEnumFromJSON(json.type),
    traceId: json.trace_id,
  };
}

export function StreamTraceDtoToJSON(json: any): StreamTraceDto {
  return StreamTraceDtoToJSONTyped(json, false);
}

export function StreamTraceDtoToJSONTyped(
  value?: StreamTraceDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: StreamDataTypeEnumToJSON(value.type),
    trace_id: value.traceId,
  };
}
