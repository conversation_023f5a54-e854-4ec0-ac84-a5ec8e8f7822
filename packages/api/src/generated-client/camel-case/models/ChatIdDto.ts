/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface ChatIdDto
 */
export interface ChatIdDto {
  /**
   * Chat ID to get details for
   * @type {string}
   * @memberof ChatIdDto
   */
  chatId: string;
}

/**
 * Check if a given object implements the ChatIdDto interface.
 */
export function instanceOfChatIdDto(value: object): value is ChatIdDto {
  if (!('chatId' in value) || value.chatId === undefined) return false;
  return true;
}

export function ChatIdDtoFromJSON(json: any): ChatIdDto {
  return ChatIdDtoFromJSONTyped(json, false);
}

export function ChatIdDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): ChatIdDto {
  if (json == null) {
    return json;
  }
  return {
    chatId: json.chatId,
  };
}

export function ChatIdDtoToJSON(json: any): ChatIdDto {
  return ChatIdDtoToJSONTyped(json, false);
}

export function ChatIdDtoToJSONTyped(
  value?: ChatIdDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    chatId: value.chatId,
  };
}
