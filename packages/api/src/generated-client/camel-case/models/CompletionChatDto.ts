/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface CompletionChatDto
 */
export interface CompletionChatDto {
  /**
   * The ID of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof CompletionChatDto
   */
  creatorId: string;
  /**
   * The creation time of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  createdAt: string;
  /**
   * The update time of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  updatedAt: string;
  /**
   * The mode of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  mode: string;
  /**
   * The title of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  title: string;
  /**
   * The origin of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  origin: string;
  /**
   * The board ID of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  boardId?: string;
}

/**
 * Check if a given object implements the CompletionChatDto interface.
 */
export function instanceOfCompletionChatDto(value: object): value is CompletionChatDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('creatorId' in value) || value.creatorId === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('mode' in value) || value.mode === undefined) return false;
  if (!('title' in value) || value.title === undefined) return false;
  if (!('origin' in value) || value.origin === undefined) return false;
  return true;
}

export function CompletionChatDtoFromJSON(json: any): CompletionChatDto {
  return CompletionChatDtoFromJSONTyped(json, false);
}

export function CompletionChatDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionChatDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    creatorId: json.creatorId,
    createdAt: json.createdAt,
    updatedAt: json.updatedAt,
    mode: json.mode,
    title: json.title,
    origin: json.origin,
    boardId: json.boardId == null ? undefined : json.boardId,
  };
}

export function CompletionChatDtoToJSON(json: any): CompletionChatDto {
  return CompletionChatDtoToJSONTyped(json, false);
}

export function CompletionChatDtoToJSONTyped(
  value?: CompletionChatDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    creatorId: value.creatorId,
    createdAt: value.createdAt,
    updatedAt: value.updatedAt,
    mode: value.mode,
    title: value.title,
    origin: value.origin,
    boardId: value.boardId,
  };
}
