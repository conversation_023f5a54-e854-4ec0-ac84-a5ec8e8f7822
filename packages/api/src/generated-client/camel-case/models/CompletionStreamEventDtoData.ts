/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { CompletionStreamAppendJsonChunkDto } from './CompletionStreamAppendJsonChunkDto';
import {
  CompletionStreamAppendJsonChunkDtoFromJSON,
  CompletionStreamAppendJsonChunkDtoFromJSONTyped,
  CompletionStreamAppendJsonChunkDtoToJSON,
  instanceOfCompletionStreamAppendJsonChunkDto,
} from './CompletionStreamAppendJsonChunkDto';
import type { CompletionStreamAppendStringChunkDto } from './CompletionStreamAppendStringChunkDto';
import {
  CompletionStreamAppendStringChunkDtoFromJSON,
  CompletionStreamAppendStringChunkDtoFromJSONTyped,
  CompletionStreamAppendStringChunkDtoToJSON,
  instanceOfCompletionStreamAppendStringChunkDto,
} from './CompletionStreamAppendStringChunkDto';
import type { CompletionStreamInsertChunkDto } from './CompletionStreamInsertChunkDto';
import {
  CompletionStreamInsertChunkDtoFromJSON,
  CompletionStreamInsertChunkDtoFromJSONTyped,
  CompletionStreamInsertChunkDtoToJSON,
  instanceOfCompletionStreamInsertChunkDto,
} from './CompletionStreamInsertChunkDto';
import type { CompletionStreamReplaceChunkDto } from './CompletionStreamReplaceChunkDto';
import {
  CompletionStreamReplaceChunkDtoFromJSON,
  CompletionStreamReplaceChunkDtoFromJSONTyped,
  CompletionStreamReplaceChunkDtoToJSON,
  instanceOfCompletionStreamReplaceChunkDto,
} from './CompletionStreamReplaceChunkDto';

/**
 * @type CompletionStreamEventDtoData
 * The completion stream chunk data
 * @export
 */
export type CompletionStreamEventDtoData =
  | CompletionStreamAppendJsonChunkDto
  | CompletionStreamAppendStringChunkDto
  | CompletionStreamInsertChunkDto
  | CompletionStreamReplaceChunkDto;

export function CompletionStreamEventDtoDataFromJSON(json: any): CompletionStreamEventDtoData {
  return CompletionStreamEventDtoDataFromJSONTyped(json, false);
}

export function CompletionStreamEventDtoDataFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionStreamEventDtoData {
  if (json == null) {
    return json;
  }
  if (typeof json !== 'object') {
    return json;
  }
  if (instanceOfCompletionStreamAppendJsonChunkDto(json)) {
    return CompletionStreamAppendJsonChunkDtoFromJSONTyped(json, true);
  }
  if (instanceOfCompletionStreamAppendStringChunkDto(json)) {
    return CompletionStreamAppendStringChunkDtoFromJSONTyped(json, true);
  }
  if (instanceOfCompletionStreamInsertChunkDto(json)) {
    return CompletionStreamInsertChunkDtoFromJSONTyped(json, true);
  }
  if (instanceOfCompletionStreamReplaceChunkDto(json)) {
    return CompletionStreamReplaceChunkDtoFromJSONTyped(json, true);
  }

  return {} as any;
}

export function CompletionStreamEventDtoDataToJSON(json: any): any {
  return CompletionStreamEventDtoDataToJSONTyped(json, false);
}

export function CompletionStreamEventDtoDataToJSONTyped(
  value?: CompletionStreamEventDtoData | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }
  if (typeof value !== 'object') {
    return value;
  }
  if (instanceOfCompletionStreamAppendJsonChunkDto(value)) {
    return CompletionStreamAppendJsonChunkDtoToJSON(value as CompletionStreamAppendJsonChunkDto);
  }
  if (instanceOfCompletionStreamAppendStringChunkDto(value)) {
    return CompletionStreamAppendStringChunkDtoToJSON(
      value as CompletionStreamAppendStringChunkDto,
    );
  }
  if (instanceOfCompletionStreamInsertChunkDto(value)) {
    return CompletionStreamInsertChunkDtoToJSON(value as CompletionStreamInsertChunkDto);
  }
  if (instanceOfCompletionStreamReplaceChunkDto(value)) {
    return CompletionStreamReplaceChunkDtoToJSON(value as CompletionStreamReplaceChunkDto);
  }

  return {};
}
