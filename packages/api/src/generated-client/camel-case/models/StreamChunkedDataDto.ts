/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { StreamDataTypeEnum } from './StreamDataTypeEnum';
import {
  StreamDataTypeEnumFromJSON,
  StreamDataTypeEnumFromJSONTyped,
  StreamDataTypeEnumToJSON,
  StreamDataTypeEnumToJSONTyped,
} from './StreamDataTypeEnum';

/**
 *
 * @export
 * @interface StreamChunkedDataDto
 */
export interface StreamChunkedDataDto {
  /**
   * The type of stream message
   * @type {StreamDataTypeEnum}
   * @memberof StreamChunkedDataDto
   */
  type: StreamDataTypeEnum;
  /**
   * The type of parent data being updated
   * @type {string}
   * @memberof StreamChunkedDataDto
   */
  parentDataType: string;
  /**
   * The ID of the parent object being updated
   * @type {string}
   * @memberof StreamChunkedDataDto
   */
  parentId: string;
  /**
   * The path to the field being updated
   * @type {string}
   * @memberof StreamChunkedDataDto
   */
  chunkPath: string;
  /**
   * The chunk data being updated
   * @type {object}
   * @memberof StreamChunkedDataDto
   */
  chunkData: object;
}

/**
 * Check if a given object implements the StreamChunkedDataDto interface.
 */
export function instanceOfStreamChunkedDataDto(value: object): value is StreamChunkedDataDto {
  if (!('type' in value) || value.type === undefined) return false;
  if (!('parentDataType' in value) || value.parentDataType === undefined) return false;
  if (!('parentId' in value) || value.parentId === undefined) return false;
  if (!('chunkPath' in value) || value.chunkPath === undefined) return false;
  if (!('chunkData' in value) || value.chunkData === undefined) return false;
  return true;
}

export function StreamChunkedDataDtoFromJSON(json: any): StreamChunkedDataDto {
  return StreamChunkedDataDtoFromJSONTyped(json, false);
}

export function StreamChunkedDataDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamChunkedDataDto {
  if (json == null) {
    return json;
  }
  return {
    type: StreamDataTypeEnumFromJSON(json.type),
    parentDataType: json.parentDataType,
    parentId: json.parentId,
    chunkPath: json.chunkPath,
    chunkData: json.chunkData,
  };
}

export function StreamChunkedDataDtoToJSON(json: any): StreamChunkedDataDto {
  return StreamChunkedDataDtoToJSONTyped(json, false);
}

export function StreamChunkedDataDtoToJSONTyped(
  value?: StreamChunkedDataDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: StreamDataTypeEnumToJSON(value.type),
    parentDataType: value.parentDataType,
    parentId: value.parentId,
    chunkPath: value.chunkPath,
    chunkData: value.chunkData,
  };
}
