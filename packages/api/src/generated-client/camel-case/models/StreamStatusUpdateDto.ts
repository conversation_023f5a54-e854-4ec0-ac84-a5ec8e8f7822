/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { StreamDataTypeEnum } from './StreamDataTypeEnum';
import {
  StreamDataTypeEnumFromJSON,
  StreamDataTypeEnumFromJSONTyped,
  StreamDataTypeEnumToJSON,
  StreamDataTypeEnumToJSONTyped,
} from './StreamDataTypeEnum';

/**
 *
 * @export
 * @interface StreamStatusUpdateDto
 */
export interface StreamStatusUpdateDto {
  /**
   * The type of stream message
   * @type {StreamDataTypeEnum}
   * @memberof StreamStatusUpdateDto
   */
  type: StreamDataTypeEnum;
  /**
   * The status being updated
   * @type {string}
   * @memberof StreamStatusUpdateDto
   */
  status: StreamStatusUpdateDtoStatusEnum;
  /**
   * Status message describing the current operation
   * @type {string}
   * @memberof StreamStatusUpdateDto
   */
  message: string;
  /**
   * Optional event data
   * @type {object}
   * @memberof StreamStatusUpdateDto
   */
  event?: object;
}

/**
 * @export
 * @enum {string}
 */
export enum StreamStatusUpdateDtoStatusEnum {
  queued = 'queued',
  generating = 'generating',
  success = 'success',
  errored = 'errored',
  aborted = 'aborted',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the StreamStatusUpdateDto interface.
 */
export function instanceOfStreamStatusUpdateDto(value: object): value is StreamStatusUpdateDto {
  if (!('type' in value) || value.type === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('message' in value) || value.message === undefined) return false;
  return true;
}

export function StreamStatusUpdateDtoFromJSON(json: any): StreamStatusUpdateDto {
  return StreamStatusUpdateDtoFromJSONTyped(json, false);
}

export function StreamStatusUpdateDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamStatusUpdateDto {
  if (json == null) {
    return json;
  }
  return {
    type: StreamDataTypeEnumFromJSON(json.type),
    status: json.status,
    message: json.message,
    event: json.event == null ? undefined : json.event,
  };
}

export function StreamStatusUpdateDtoToJSON(json: any): StreamStatusUpdateDto {
  return StreamStatusUpdateDtoToJSONTyped(json, false);
}

export function StreamStatusUpdateDtoToJSONTyped(
  value?: StreamStatusUpdateDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: StreamDataTypeEnumToJSON(value.type),
    status: value.status,
    message: value.message,
    event: value.event,
  };
}
