/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { StreamDataTypeEnum } from './StreamDataTypeEnum';
import {
  StreamDataTypeEnumFromJSON,
  StreamDataTypeEnumFromJSONTyped,
  StreamDataTypeEnumToJSON,
  StreamDataTypeEnumToJSONTyped,
} from './StreamDataTypeEnum';

/**
 *
 * @export
 * @interface StreamTextStopDto
 */
export interface StreamTextStopDto {
  /**
   * The type of stream message
   * @type {StreamDataTypeEnum}
   * @memberof StreamTextStopDto
   */
  type: StreamDataTypeEnum;
}

/**
 * Check if a given object implements the StreamTextStopDto interface.
 */
export function instanceOfStreamTextStopDto(value: object): value is StreamTextStopDto {
  if (!('type' in value) || value.type === undefined) return false;
  return true;
}

export function StreamTextStopDtoFromJSON(json: any): StreamTextStopDto {
  return StreamTextStopDtoFromJSONTyped(json, false);
}

export function StreamTextStopDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamTextStopDto {
  if (json == null) {
    return json;
  }
  return {
    type: StreamDataTypeEnumFromJSON(json.type),
  };
}

export function StreamTextStopDtoToJSON(json: any): StreamTextStopDto {
  return StreamTextStopDtoToJSONTyped(json, false);
}

export function StreamTextStopDtoToJSONTyped(
  value?: StreamTextStopDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: StreamDataTypeEnumToJSON(value.type),
  };
}
