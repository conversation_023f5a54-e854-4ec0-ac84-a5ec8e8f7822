/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * The mode of the stream chunk operation
 * @export
 * @enum {string}
 */
export enum CompletionStreamModeEnum {
  insert = 'insert',
  replace = 'replace',
  appendJson = 'append_json',
  appendString = 'append_string',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfCompletionStreamModeEnum(value: any): boolean {
  for (const key in CompletionStreamModeEnum) {
    if (Object.hasOwn(CompletionStreamModeEnum, key)) {
      if (CompletionStreamModeEnum[key as keyof typeof CompletionStreamModeEnum] === value) {
        return true;
      }
    }
  }
  return false;
}

export function CompletionStreamModeEnumFromJSON(json: any): CompletionStreamModeEnum {
  return CompletionStreamModeEnumFromJSONTyped(json, false);
}

export function CompletionStreamModeEnumFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionStreamModeEnum {
  return json as CompletionStreamModeEnum;
}

export function CompletionStreamModeEnumToJSON(value?: CompletionStreamModeEnum | null): any {
  return value as any;
}

export function CompletionStreamModeEnumToJSONTyped(
  value: any,
  _ignoreDiscriminator: boolean,
): CompletionStreamModeEnum {
  return value as CompletionStreamModeEnum;
}
