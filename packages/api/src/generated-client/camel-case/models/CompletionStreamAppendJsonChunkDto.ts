/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CompletionStreamModeEnum } from './CompletionStreamModeEnum';
import {
  CompletionStreamModeEnumFromJSON,
  CompletionStreamModeEnumFromJSONTyped,
  CompletionStreamModeEnumToJSON,
  CompletionStreamModeEnumToJSONTyped,
} from './CompletionStreamModeEnum';

/**
 *
 * @export
 * @interface CompletionStreamAppendJsonChunkDto
 */
export interface CompletionStreamAppendJsonChunkDto {
  /**
   * The mode of the stream chunk operation
   * @type {CompletionStreamModeEnum}
   * @memberof CompletionStreamAppendJsonChunkDto
   */
  mode: CompletionStreamModeEnum;
  /**
   * The type of target being appended to
   * @type {string}
   * @memberof CompletionStreamAppendJsonChunkDto
   */
  targetType: CompletionStreamAppendJsonChunkDtoTargetTypeEnum;
  /**
   * The ID of the target being appended to
   * @type {string}
   * @memberof CompletionStreamAppendJsonChunkDto
   */
  targetId: string;
  /**
   * The JSON string data to append
   * @type {string}
   * @memberof CompletionStreamAppendJsonChunkDto
   */
  data: string;
  /**
   * The path of the field being appended to
   * @type {string}
   * @memberof CompletionStreamAppendJsonChunkDto
   */
  path: string;
}

/**
 * @export
 * @enum {string}
 */
export enum CompletionStreamAppendJsonChunkDtoTargetTypeEnum {
  chat = 'Chat',
  message = 'Message',
  completionBlock = 'CompletionBlock',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the CompletionStreamAppendJsonChunkDto interface.
 */
export function instanceOfCompletionStreamAppendJsonChunkDto(
  value: object,
): value is CompletionStreamAppendJsonChunkDto {
  if (!('mode' in value) || value.mode === undefined) return false;
  if (!('targetType' in value) || value.targetType === undefined) return false;
  if (!('targetId' in value) || value.targetId === undefined) return false;
  if (!('data' in value) || value.data === undefined) return false;
  if (!('path' in value) || value.path === undefined) return false;
  return true;
}

export function CompletionStreamAppendJsonChunkDtoFromJSON(
  json: any,
): CompletionStreamAppendJsonChunkDto {
  return CompletionStreamAppendJsonChunkDtoFromJSONTyped(json, false);
}

export function CompletionStreamAppendJsonChunkDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionStreamAppendJsonChunkDto {
  if (json == null) {
    return json;
  }
  return {
    mode: CompletionStreamModeEnumFromJSON(json.mode),
    targetType: json.targetType,
    targetId: json.targetId,
    data: json.data,
    path: json.path,
  };
}

export function CompletionStreamAppendJsonChunkDtoToJSON(
  json: any,
): CompletionStreamAppendJsonChunkDto {
  return CompletionStreamAppendJsonChunkDtoToJSONTyped(json, false);
}

export function CompletionStreamAppendJsonChunkDtoToJSONTyped(
  value?: CompletionStreamAppendJsonChunkDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    mode: CompletionStreamModeEnumToJSON(value.mode),
    targetType: value.targetType,
    targetId: value.targetId,
    data: value.data,
    path: value.path,
  };
}
