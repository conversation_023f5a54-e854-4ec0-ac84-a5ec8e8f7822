/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CompletionStreamModeEnum } from './CompletionStreamModeEnum';
import {
  CompletionStreamModeEnumFromJSON,
  CompletionStreamModeEnumFromJSONTyped,
  CompletionStreamModeEnumToJSON,
  CompletionStreamModeEnumToJSONTyped,
} from './CompletionStreamModeEnum';

/**
 *
 * @export
 * @interface CompletionStreamReplaceChunkDto
 */
export interface CompletionStreamReplaceChunkDto {
  /**
   * The mode of the stream chunk operation
   * @type {CompletionStreamModeEnum}
   * @memberof CompletionStreamReplaceChunkDto
   */
  mode: CompletionStreamModeEnum;
  /**
   * The type of target being replaced
   * @type {string}
   * @memberof CompletionStreamReplaceChunkDto
   */
  targetType: CompletionStreamReplaceChunkDtoTargetTypeEnum;
  /**
   * The ID of the target being replaced
   * @type {string}
   * @memberof CompletionStreamReplaceChunkDto
   */
  targetId: string;
  /**
   * The new data to replace with
   * @type {object}
   * @memberof CompletionStreamReplaceChunkDto
   */
  data: object;
  /**
   * The path of the field being replaced
   * @type {string}
   * @memberof CompletionStreamReplaceChunkDto
   */
  path: string;
}

/**
 * @export
 * @enum {string}
 */
export enum CompletionStreamReplaceChunkDtoTargetTypeEnum {
  chat = 'Chat',
  message = 'Message',
  completionBlock = 'CompletionBlock',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the CompletionStreamReplaceChunkDto interface.
 */
export function instanceOfCompletionStreamReplaceChunkDto(
  value: object,
): value is CompletionStreamReplaceChunkDto {
  if (!('mode' in value) || value.mode === undefined) return false;
  if (!('targetType' in value) || value.targetType === undefined) return false;
  if (!('targetId' in value) || value.targetId === undefined) return false;
  if (!('data' in value) || value.data === undefined) return false;
  if (!('path' in value) || value.path === undefined) return false;
  return true;
}

export function CompletionStreamReplaceChunkDtoFromJSON(
  json: any,
): CompletionStreamReplaceChunkDto {
  return CompletionStreamReplaceChunkDtoFromJSONTyped(json, false);
}

export function CompletionStreamReplaceChunkDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionStreamReplaceChunkDto {
  if (json == null) {
    return json;
  }
  return {
    mode: CompletionStreamModeEnumFromJSON(json.mode),
    targetType: json.targetType,
    targetId: json.targetId,
    data: json.data,
    path: json.path,
  };
}

export function CompletionStreamReplaceChunkDtoToJSON(json: any): CompletionStreamReplaceChunkDto {
  return CompletionStreamReplaceChunkDtoToJSONTyped(json, false);
}

export function CompletionStreamReplaceChunkDtoToJSONTyped(
  value?: CompletionStreamReplaceChunkDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    mode: CompletionStreamModeEnumToJSON(value.mode),
    targetType: value.targetType,
    targetId: value.targetId,
    data: value.data,
    path: value.path,
  };
}
