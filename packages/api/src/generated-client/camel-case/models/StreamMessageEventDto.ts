/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { StreamMessageEventDtoData } from './StreamMessageEventDtoData';
import {
  StreamMessageEventDtoDataFromJSON,
  StreamMessageEventDtoDataFromJSONTyped,
  StreamMessageEventDtoDataToJSON,
  StreamMessageEventDtoDataToJSONTyped,
} from './StreamMessageEventDtoData';

/**
 *
 * @export
 * @interface StreamMessageEventDto
 */
export interface StreamMessageEventDto {
  /**
   * Event ID for SSE
   * @type {string}
   * @memberof StreamMessageEventDto
   */
  id?: string;
  /**
   * Event type for SSE
   * @type {string}
   * @memberof StreamMessageEventDto
   */
  event?: string;
  /**
   *
   * @type {StreamMessageEventDtoData}
   * @memberof StreamMessageEventDto
   */
  data: StreamMessageEventDtoData;
}

/**
 * Check if a given object implements the StreamMessageEventDto interface.
 */
export function instanceOfStreamMessageEventDto(value: object): value is StreamMessageEventDto {
  if (!('data' in value) || value.data === undefined) return false;
  return true;
}

export function StreamMessageEventDtoFromJSON(json: any): StreamMessageEventDto {
  return StreamMessageEventDtoFromJSONTyped(json, false);
}

export function StreamMessageEventDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamMessageEventDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id == null ? undefined : json.id,
    event: json.event == null ? undefined : json.event,
    data: StreamMessageEventDtoDataFromJSON(json.data),
  };
}

export function StreamMessageEventDtoToJSON(json: any): StreamMessageEventDto {
  return StreamMessageEventDtoToJSONTyped(json, false);
}

export function StreamMessageEventDtoToJSONTyped(
  value?: StreamMessageEventDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    event: value.event,
    data: StreamMessageEventDtoDataToJSON(value.data),
  };
}
