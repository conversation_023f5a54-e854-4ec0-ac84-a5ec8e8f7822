/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { CompletionStreamAppendJsonChunkDto } from './CompletionStreamAppendJsonChunkDto';
import {
  CompletionStreamAppendJsonChunkDtoFromJSON,
  CompletionStreamAppendJsonChunkDtoFromJSONTyped,
  CompletionStreamAppendJsonChunkDtoToJSON,
  instanceOfCompletionStreamAppendJsonChunkDto,
} from './CompletionStreamAppendJsonChunkDto';
import type { CompletionStreamAppendStringChunkDto } from './CompletionStreamAppendStringChunkDto';
import {
  CompletionStreamAppendStringChunkDtoFromJSON,
  CompletionStreamAppendStringChunkDtoFromJSONTyped,
  CompletionStreamAppendStringChunkDtoToJSON,
  instanceOfCompletionStreamAppendStringChunkDto,
} from './CompletionStreamAppendStringChunkDto';
import type { CompletionStreamEventDto } from './CompletionStreamEventDto';
import {
  CompletionStreamEventDtoFromJSON,
  CompletionStreamEventDtoFromJSONTyped,
  CompletionStreamEventDtoToJSON,
  instanceOfCompletionStreamEventDto,
} from './CompletionStreamEventDto';
import type { CompletionStreamInsertChunkDto } from './CompletionStreamInsertChunkDto';
import {
  CompletionStreamInsertChunkDtoFromJSON,
  CompletionStreamInsertChunkDtoFromJSONTyped,
  CompletionStreamInsertChunkDtoToJSON,
  instanceOfCompletionStreamInsertChunkDto,
} from './CompletionStreamInsertChunkDto';
import type { CompletionStreamReplaceChunkDto } from './CompletionStreamReplaceChunkDto';
import {
  CompletionStreamReplaceChunkDtoFromJSON,
  CompletionStreamReplaceChunkDtoFromJSONTyped,
  CompletionStreamReplaceChunkDtoToJSON,
  instanceOfCompletionStreamReplaceChunkDto,
} from './CompletionStreamReplaceChunkDto';

/**
 * @type ChatV2ControllerCreateChat200Response
 *
 * @export
 */
export type ChatV2ControllerCreateChat200Response =
  | CompletionStreamAppendJsonChunkDto
  | CompletionStreamAppendStringChunkDto
  | CompletionStreamEventDto
  | CompletionStreamInsertChunkDto
  | CompletionStreamReplaceChunkDto;

export function ChatV2ControllerCreateChat200ResponseFromJSON(
  json: any,
): ChatV2ControllerCreateChat200Response {
  return ChatV2ControllerCreateChat200ResponseFromJSONTyped(json, false);
}

export function ChatV2ControllerCreateChat200ResponseFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ChatV2ControllerCreateChat200Response {
  if (json == null) {
    return json;
  }
  if (typeof json !== 'object') {
    return json;
  }
  if (instanceOfCompletionStreamAppendJsonChunkDto(json)) {
    return CompletionStreamAppendJsonChunkDtoFromJSONTyped(json, true);
  }
  if (instanceOfCompletionStreamAppendStringChunkDto(json)) {
    return CompletionStreamAppendStringChunkDtoFromJSONTyped(json, true);
  }
  if (instanceOfCompletionStreamEventDto(json)) {
    return CompletionStreamEventDtoFromJSONTyped(json, true);
  }
  if (instanceOfCompletionStreamInsertChunkDto(json)) {
    return CompletionStreamInsertChunkDtoFromJSONTyped(json, true);
  }
  if (instanceOfCompletionStreamReplaceChunkDto(json)) {
    return CompletionStreamReplaceChunkDtoFromJSONTyped(json, true);
  }

  return {} as any;
}

export function ChatV2ControllerCreateChat200ResponseToJSON(json: any): any {
  return ChatV2ControllerCreateChat200ResponseToJSONTyped(json, false);
}

export function ChatV2ControllerCreateChat200ResponseToJSONTyped(
  value?: ChatV2ControllerCreateChat200Response | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }
  if (typeof value !== 'object') {
    return value;
  }
  if (instanceOfCompletionStreamAppendJsonChunkDto(value)) {
    return CompletionStreamAppendJsonChunkDtoToJSON(value as CompletionStreamAppendJsonChunkDto);
  }
  if (instanceOfCompletionStreamAppendStringChunkDto(value)) {
    return CompletionStreamAppendStringChunkDtoToJSON(
      value as CompletionStreamAppendStringChunkDto,
    );
  }
  if (instanceOfCompletionStreamEventDto(value)) {
    return CompletionStreamEventDtoToJSON(value as CompletionStreamEventDto);
  }
  if (instanceOfCompletionStreamInsertChunkDto(value)) {
    return CompletionStreamInsertChunkDtoToJSON(value as CompletionStreamInsertChunkDto);
  }
  if (instanceOfCompletionStreamReplaceChunkDto(value)) {
    return CompletionStreamReplaceChunkDtoToJSON(value as CompletionStreamReplaceChunkDto);
  }

  return {};
}
