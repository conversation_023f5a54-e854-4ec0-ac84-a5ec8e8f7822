/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { StreamDataTypeEnum } from './StreamDataTypeEnum';
import {
  StreamDataTypeEnumFromJSON,
  StreamDataTypeEnumFromJSONTyped,
  StreamDataTypeEnumToJSON,
  StreamDataTypeEnumToJSONTyped,
} from './StreamDataTypeEnum';

/**
 *
 * @export
 * @interface StreamTextStartDto
 */
export interface StreamTextStartDto {
  /**
   * The type of stream message
   * @type {StreamDataTypeEnum}
   * @memberof StreamTextStartDto
   */
  type: StreamDataTypeEnum;
}

/**
 * Check if a given object implements the StreamTextStartDto interface.
 */
export function instanceOfStreamTextStartDto(value: object): value is StreamTextStartDto {
  if (!('type' in value) || value.type === undefined) return false;
  return true;
}

export function StreamTextStartDtoFromJSON(json: any): StreamTextStartDto {
  return StreamTextStartDtoFromJSONTyped(json, false);
}

export function StreamTextStartDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamTextStartDto {
  if (json == null) {
    return json;
  }
  return {
    type: StreamDataTypeEnumFromJSON(json.type),
  };
}

export function StreamTextStartDtoToJSON(json: any): StreamTextStartDto {
  return StreamTextStartDtoToJSONTyped(json, false);
}

export function StreamTextStartDtoToJSONTyped(
  value?: StreamTextStartDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: StreamDataTypeEnumToJSON(value.type),
  };
}
