/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { StreamDataTypeEnum } from './StreamDataTypeEnum';
import {
  StreamDataTypeEnumFromJSON,
  StreamDataTypeEnumFromJSONTyped,
  StreamDataTypeEnumToJSON,
  StreamDataTypeEnumToJSONTyped,
} from './StreamDataTypeEnum';

/**
 *
 * @export
 * @interface StreamDataDto
 */
export interface StreamDataDto {
  /**
   * The type of stream message
   * @type {StreamDataTypeEnum}
   * @memberof StreamDataDto
   */
  type: StreamDataTypeEnum;
  /**
   * The type of data being streamed
   * @type {string}
   * @memberof StreamDataDto
   */
  dataType: string;
  /**
   * The data payload
   * @type {object}
   * @memberof StreamDataDto
   */
  data: object;
  /**
   * The ID of the data object
   * @type {string}
   * @memberof StreamDataDto
   */
  id: string;
}

/**
 * Check if a given object implements the StreamDataDto interface.
 */
export function instanceOfStreamDataDto(value: object): value is StreamDataDto {
  if (!('type' in value) || value.type === undefined) return false;
  if (!('dataType' in value) || value.dataType === undefined) return false;
  if (!('data' in value) || value.data === undefined) return false;
  if (!('id' in value) || value.id === undefined) return false;
  return true;
}

export function StreamDataDtoFromJSON(json: any): StreamDataDto {
  return StreamDataDtoFromJSONTyped(json, false);
}

export function StreamDataDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamDataDto {
  if (json == null) {
    return json;
  }
  return {
    type: StreamDataTypeEnumFromJSON(json.type),
    dataType: json.dataType,
    data: json.data,
    id: json.id,
  };
}

export function StreamDataDtoToJSON(json: any): StreamDataDto {
  return StreamDataDtoToJSONTyped(json, false);
}

export function StreamDataDtoToJSONTyped(
  value?: StreamDataDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: StreamDataTypeEnumToJSON(value.type),
    dataType: value.dataType,
    data: value.data,
    id: value.id,
  };
}
