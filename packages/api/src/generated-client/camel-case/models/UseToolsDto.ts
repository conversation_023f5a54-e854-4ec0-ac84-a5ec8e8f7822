/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ImageGenerateToolDto } from './ImageGenerateToolDto';
import {
  ImageGenerateToolDtoFromJSON,
  ImageGenerateToolDtoFromJSONTyped,
  ImageGenerateToolDtoToJSON,
  ImageGenerateToolDtoToJSONTyped,
} from './ImageGenerateToolDto';
import type { ToolOptionsDto } from './ToolOptionsDto';
import {
  ToolOptionsDtoFromJSON,
  ToolOptionsDtoFromJSONTyped,
  ToolOptionsDtoToJSON,
  ToolOptionsDtoToJSONTyped,
} from './ToolOptionsDto';

/**
 *
 * @export
 * @interface UseToolsDto
 */
export interface UseToolsDto {
  /**
   * Image generation tool options
   * @type {ImageGenerateToolDto}
   * @memberof UseToolsDto
   */
  imageGenerate?: ImageGenerateToolDto;
  /**
   * Diagram generation tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  diagramGenerate?: ToolOptionsDto;
  /**
   * Google search tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  googleSearch?: ToolOptionsDto;
  /**
   * Library search tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  librarySearch?: ToolOptionsDto;
  /**
   * Edit thought tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  editThought?: ToolOptionsDto;
  /**
   * Audio generation tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  audioGenerate?: ToolOptionsDto;
  /**
   * Board search tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  boardSearch?: ToolOptionsDto;
}

/**
 * Check if a given object implements the UseToolsDto interface.
 */
export function instanceOfUseToolsDto(value: object): value is UseToolsDto {
  return true;
}

export function UseToolsDtoFromJSON(json: any): UseToolsDto {
  return UseToolsDtoFromJSONTyped(json, false);
}

export function UseToolsDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): UseToolsDto {
  if (json == null) {
    return json;
  }
  return {
    imageGenerate:
      json.image_generate == null ? undefined : ImageGenerateToolDtoFromJSON(json.image_generate),
    diagramGenerate:
      json.diagram_generate == null ? undefined : ToolOptionsDtoFromJSON(json.diagram_generate),
    googleSearch:
      json.google_search == null ? undefined : ToolOptionsDtoFromJSON(json.google_search),
    librarySearch:
      json.library_search == null ? undefined : ToolOptionsDtoFromJSON(json.library_search),
    editThought: json.edit_thought == null ? undefined : ToolOptionsDtoFromJSON(json.edit_thought),
    audioGenerate:
      json.audio_generate == null ? undefined : ToolOptionsDtoFromJSON(json.audio_generate),
    boardSearch: json.board_search == null ? undefined : ToolOptionsDtoFromJSON(json.board_search),
  };
}

export function UseToolsDtoToJSON(json: any): UseToolsDto {
  return UseToolsDtoToJSONTyped(json, false);
}

export function UseToolsDtoToJSONTyped(
  value?: UseToolsDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    image_generate: ImageGenerateToolDtoToJSON(value.imageGenerate),
    diagram_generate: ToolOptionsDtoToJSON(value.diagramGenerate),
    google_search: ToolOptionsDtoToJSON(value.googleSearch),
    library_search: ToolOptionsDtoToJSON(value.librarySearch),
    edit_thought: ToolOptionsDtoToJSON(value.editThought),
    audio_generate: ToolOptionsDtoToJSON(value.audioGenerate),
    board_search: ToolOptionsDtoToJSON(value.boardSearch),
  };
}
