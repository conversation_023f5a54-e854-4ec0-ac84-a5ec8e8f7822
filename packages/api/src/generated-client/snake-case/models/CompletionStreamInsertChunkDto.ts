/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CompletionStreamInsertChunkDtoData } from './CompletionStreamInsertChunkDtoData';
import {
  CompletionStreamInsertChunkDtoDataFromJSON,
  CompletionStreamInsertChunkDtoDataFromJSONTyped,
  CompletionStreamInsertChunkDtoDataToJSON,
  CompletionStreamInsertChunkDtoDataToJSONTyped,
} from './CompletionStreamInsertChunkDtoData';
import type { CompletionStreamModeEnum } from './CompletionStreamModeEnum';
import {
  CompletionStreamModeEnumFromJSON,
  CompletionStreamModeEnumFromJSONTyped,
  CompletionStreamModeEnumToJSON,
  CompletionStreamModeEnumToJSONTyped,
} from './CompletionStreamModeEnum';

/**
 *
 * @export
 * @interface CompletionStreamInsertChunkDto
 */
export interface CompletionStreamInsertChunkDto {
  /**
   * The mode of the stream chunk operation
   * @type {CompletionStreamModeEnum}
   * @memberof CompletionStreamInsertChunkDto
   */
  mode: CompletionStreamModeEnum;
  /**
   * The type of data being inserted
   * @type {string}
   * @memberof CompletionStreamInsertChunkDto
   */
  data_type: CompletionStreamInsertChunkDtoDataTypeEnum;
  /**
   *
   * @type {CompletionStreamInsertChunkDtoData}
   * @memberof CompletionStreamInsertChunkDto
   */
  data: CompletionStreamInsertChunkDtoData;
}

/**
 * @export
 * @enum {string}
 */
export enum CompletionStreamInsertChunkDtoDataTypeEnum {
  chat = 'Chat',
  message = 'Message',
  completionBlock = 'CompletionBlock',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the CompletionStreamInsertChunkDto interface.
 */
export function instanceOfCompletionStreamInsertChunkDto(
  value: object,
): value is CompletionStreamInsertChunkDto {
  if (!('mode' in value) || value.mode === undefined) return false;
  if (!('data_type' in value) || value.data_type === undefined) return false;
  if (!('data' in value) || value.data === undefined) return false;
  return true;
}

export function CompletionStreamInsertChunkDtoFromJSON(json: any): CompletionStreamInsertChunkDto {
  return CompletionStreamInsertChunkDtoFromJSONTyped(json, false);
}

export function CompletionStreamInsertChunkDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionStreamInsertChunkDto {
  if (json == null) {
    return json;
  }
  return {
    mode: CompletionStreamModeEnumFromJSON(json.mode),
    data_type: json.data_type,
    data: CompletionStreamInsertChunkDtoDataFromJSON(json.data),
  };
}

export function CompletionStreamInsertChunkDtoToJSON(json: any): CompletionStreamInsertChunkDto {
  return CompletionStreamInsertChunkDtoToJSONTyped(json, false);
}

export function CompletionStreamInsertChunkDtoToJSONTyped(
  value?: CompletionStreamInsertChunkDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    mode: CompletionStreamModeEnumToJSON(value.mode),
    data_type: value.data_type,
    data: CompletionStreamInsertChunkDtoDataToJSON(value.data),
  };
}
