/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { StreamDataTypeEnum } from './StreamDataTypeEnum';
import {
  StreamDataTypeEnumFromJSON,
  StreamDataTypeEnumFromJSONTyped,
  StreamDataTypeEnumToJSON,
  StreamDataTypeEnumToJSONTyped,
} from './StreamDataTypeEnum';

/**
 *
 * @export
 * @interface StreamTextDto
 */
export interface StreamTextDto {
  /**
   * The type of stream message
   * @type {StreamDataTypeEnum}
   * @memberof StreamTextDto
   */
  type: StreamDataTypeEnum;
  /**
   * The text content being streamed
   * @type {string}
   * @memberof StreamTextDto
   */
  data: string;
  /**
   * The type of content being streamed
   * @type {string}
   * @memberof StreamTextDto
   */
  content_type?: StreamTextDtoContentTypeEnum;
}

/**
 * @export
 * @enum {string}
 */
export enum StreamTextDtoContentTypeEnum {
  content = 'content',
  reasoning = 'reasoning',
  error = 'error',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the StreamTextDto interface.
 */
export function instanceOfStreamTextDto(value: object): value is StreamTextDto {
  if (!('type' in value) || value.type === undefined) return false;
  if (!('data' in value) || value.data === undefined) return false;
  return true;
}

export function StreamTextDtoFromJSON(json: any): StreamTextDto {
  return StreamTextDtoFromJSONTyped(json, false);
}

export function StreamTextDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamTextDto {
  if (json == null) {
    return json;
  }
  return {
    type: StreamDataTypeEnumFromJSON(json.type),
    data: json.data,
    content_type: json.content_type == null ? undefined : json.content_type,
  };
}

export function StreamTextDtoToJSON(json: any): StreamTextDto {
  return StreamTextDtoToJSONTyped(json, false);
}

export function StreamTextDtoToJSONTyped(
  value?: StreamTextDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: StreamDataTypeEnumToJSON(value.type),
    data: value.data,
    content_type: value.content_type,
  };
}
