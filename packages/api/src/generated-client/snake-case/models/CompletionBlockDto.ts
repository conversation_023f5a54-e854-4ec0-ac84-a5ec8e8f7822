/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface CompletionBlockDto
 */
export interface CompletionBlockDto {
  /**
   * The ID of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  id: string;
  /**
   * The ID of the chat
   * @type {string}
   * @memberof CompletionBlockDto
   */
  message_id: string;
  /**
   * The type of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  type: string;
  /**
   * The status of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  status: string;
  /**
   * The extra of the block
   * @type {object}
   * @memberof CompletionBlockDto
   */
  extra: object;
  /**
   * The data of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  data?: string;
  /**
   * The tool ID of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  tool_id?: string;
  /**
   * The tool name of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  tool_name?: string;
  /**
   * The tool arguments of the block
   * @type {object}
   * @memberof CompletionBlockDto
   */
  tool_arguments?: object;
  /**
   * The tool result of the block
   * @type {object}
   * @memberof CompletionBlockDto
   */
  tool_result?: object;
  /**
   * The tool response of the block
   * @type {string}
   * @memberof CompletionBlockDto
   */
  tool_response?: string;
}

/**
 * Check if a given object implements the CompletionBlockDto interface.
 */
export function instanceOfCompletionBlockDto(value: object): value is CompletionBlockDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('message_id' in value) || value.message_id === undefined) return false;
  if (!('type' in value) || value.type === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('extra' in value) || value.extra === undefined) return false;
  return true;
}

export function CompletionBlockDtoFromJSON(json: any): CompletionBlockDto {
  return CompletionBlockDtoFromJSONTyped(json, false);
}

export function CompletionBlockDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionBlockDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    message_id: json.message_id,
    type: json.type,
    status: json.status,
    extra: json.extra,
    data: json.data == null ? undefined : json.data,
    tool_id: json.tool_id == null ? undefined : json.tool_id,
    tool_name: json.tool_name == null ? undefined : json.tool_name,
    tool_arguments: json.tool_arguments == null ? undefined : json.tool_arguments,
    tool_result: json.tool_result == null ? undefined : json.tool_result,
    tool_response: json.tool_response == null ? undefined : json.tool_response,
  };
}

export function CompletionBlockDtoToJSON(json: any): CompletionBlockDto {
  return CompletionBlockDtoToJSONTyped(json, false);
}

export function CompletionBlockDtoToJSONTyped(
  value?: CompletionBlockDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    message_id: value.message_id,
    type: value.type,
    status: value.status,
    extra: value.extra,
    data: value.data,
    tool_id: value.tool_id,
    tool_name: value.tool_name,
    tool_arguments: value.tool_arguments,
    tool_result: value.tool_result,
    tool_response: value.tool_response,
  };
}
