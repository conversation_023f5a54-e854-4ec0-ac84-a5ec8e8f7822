/* tslint:disable */
/* eslint-disable */
/**
 * YouAP<PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface ShortcutDto
 */
export interface ShortcutDto {
  /**
   * Shortcut ID
   * @type {string}
   * @memberof ShortcutDto
   */
  id: string;
  /**
   * Shortcut name
   * @type {string}
   * @memberof ShortcutDto
   */
  name: string;
}

/**
 * Check if a given object implements the ShortcutDto interface.
 */
export function instanceOfShortcutDto(value: object): value is ShortcutDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('name' in value) || value.name === undefined) return false;
  return true;
}

export function ShortcutDtoFromJSON(json: any): ShortcutDto {
  return ShortcutDtoFromJSONTyped(json, false);
}

export function ShortcutDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): ShortcutDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    name: json.name,
  };
}

export function ShortcutDtoToJSON(json: any): ShortcutDto {
  return ShortcutDtoToJSONTyped(json, false);
}

export function ShortcutDtoToJSONTyped(
  value?: ShortcutDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    name: value.name,
  };
}
