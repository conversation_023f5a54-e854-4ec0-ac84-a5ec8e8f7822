/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { StreamChunkedDataDto } from './StreamChunkedDataDto';
import {
  instanceOfStreamChunkedDataDto,
  StreamChunkedDataDtoFromJSON,
  StreamChunkedDataDtoFromJSONTyped,
  StreamChunkedDataDtoToJSON,
} from './StreamChunkedDataDto';
import type { StreamDataDto } from './StreamDataDto';
import {
  instanceOfStreamDataDto,
  StreamDataDtoFromJSON,
  StreamDataDtoFromJSONTyped,
  StreamDataDtoToJSON,
} from './StreamDataDto';
import type { StreamErrorDto } from './StreamErrorDto';
import {
  instanceOfStreamErrorDto,
  StreamErrorDtoFromJSON,
  StreamErrorDtoFromJSONTyped,
  StreamErrorDtoToJSON,
} from './StreamErrorDto';
import type { StreamStatusUpdateDto } from './StreamStatusUpdateDto';
import {
  instanceOfStreamStatusUpdateDto,
  StreamStatusUpdateDtoFromJSON,
  StreamStatusUpdateDtoFromJSONTyped,
  StreamStatusUpdateDtoToJSON,
} from './StreamStatusUpdateDto';
import type { StreamTextDto } from './StreamTextDto';
import {
  instanceOfStreamTextDto,
  StreamTextDtoFromJSON,
  StreamTextDtoFromJSONTyped,
  StreamTextDtoToJSON,
} from './StreamTextDto';
import type { StreamTextStartDto } from './StreamTextStartDto';
import {
  instanceOfStreamTextStartDto,
  StreamTextStartDtoFromJSON,
  StreamTextStartDtoFromJSONTyped,
  StreamTextStartDtoToJSON,
} from './StreamTextStartDto';
import type { StreamTextStopDto } from './StreamTextStopDto';
import {
  instanceOfStreamTextStopDto,
  StreamTextStopDtoFromJSON,
  StreamTextStopDtoFromJSONTyped,
  StreamTextStopDtoToJSON,
} from './StreamTextStopDto';
import type { StreamTraceDto } from './StreamTraceDto';
import {
  instanceOfStreamTraceDto,
  StreamTraceDtoFromJSON,
  StreamTraceDtoFromJSONTyped,
  StreamTraceDtoToJSON,
} from './StreamTraceDto';

/**
 * @type StreamMessageEventDtoData
 * The stream message data
 * @export
 */
export type StreamMessageEventDtoData =
  | StreamChunkedDataDto
  | StreamDataDto
  | StreamErrorDto
  | StreamStatusUpdateDto
  | StreamTextDto
  | StreamTextStartDto
  | StreamTextStopDto
  | StreamTraceDto;

export function StreamMessageEventDtoDataFromJSON(json: any): StreamMessageEventDtoData {
  return StreamMessageEventDtoDataFromJSONTyped(json, false);
}

export function StreamMessageEventDtoDataFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamMessageEventDtoData {
  if (json == null) {
    return json;
  }
  if (typeof json !== 'object') {
    return json;
  }
  if (instanceOfStreamChunkedDataDto(json)) {
    return StreamChunkedDataDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamDataDto(json)) {
    return StreamDataDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamErrorDto(json)) {
    return StreamErrorDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamStatusUpdateDto(json)) {
    return StreamStatusUpdateDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamTextDto(json)) {
    return StreamTextDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamTextStartDto(json)) {
    return StreamTextStartDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamTextStopDto(json)) {
    return StreamTextStopDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamTraceDto(json)) {
    return StreamTraceDtoFromJSONTyped(json, true);
  }

  return {} as any;
}

export function StreamMessageEventDtoDataToJSON(json: any): any {
  return StreamMessageEventDtoDataToJSONTyped(json, false);
}

export function StreamMessageEventDtoDataToJSONTyped(
  value?: StreamMessageEventDtoData | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }
  if (typeof value !== 'object') {
    return value;
  }
  if (instanceOfStreamChunkedDataDto(value)) {
    return StreamChunkedDataDtoToJSON(value as StreamChunkedDataDto);
  }
  if (instanceOfStreamDataDto(value)) {
    return StreamDataDtoToJSON(value as StreamDataDto);
  }
  if (instanceOfStreamErrorDto(value)) {
    return StreamErrorDtoToJSON(value as StreamErrorDto);
  }
  if (instanceOfStreamStatusUpdateDto(value)) {
    return StreamStatusUpdateDtoToJSON(value as StreamStatusUpdateDto);
  }
  if (instanceOfStreamTextDto(value)) {
    return StreamTextDtoToJSON(value as StreamTextDto);
  }
  if (instanceOfStreamTextStartDto(value)) {
    return StreamTextStartDtoToJSON(value as StreamTextStartDto);
  }
  if (instanceOfStreamTextStopDto(value)) {
    return StreamTextStopDtoToJSON(value as StreamTextStopDto);
  }
  if (instanceOfStreamTraceDto(value)) {
    return StreamTraceDtoToJSON(value as StreamTraceDto);
  }

  return {};
}
