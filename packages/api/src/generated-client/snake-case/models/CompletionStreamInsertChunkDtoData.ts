/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { CompletionBlockDto } from './CompletionBlockDto';
import {
  CompletionBlockDtoFromJSON,
  CompletionBlockDtoFromJSONTyped,
  CompletionBlockDtoToJSON,
  instanceOfCompletionBlockDto,
} from './CompletionBlockDto';
import type { CompletionChatDto } from './CompletionChatDto';
import {
  CompletionChatDtoFromJSON,
  CompletionChatDtoFromJSONTyped,
  CompletionChatDtoToJSON,
  instanceOfCompletionChatDto,
} from './CompletionChatDto';
import type { CompletionMessageDto } from './CompletionMessageDto';
import {
  CompletionMessageDtoFromJSON,
  CompletionMessageDtoFromJSONTyped,
  CompletionMessageDtoToJSON,
  instanceOfCompletionMessageDto,
} from './CompletionMessageDto';

/**
 * @type CompletionStreamInsertChunkDtoData
 * The data payload being inserted
 * @export
 */
export type CompletionStreamInsertChunkDtoData =
  | CompletionBlockDto
  | CompletionChatDto
  | CompletionMessageDto;

export function CompletionStreamInsertChunkDtoDataFromJSON(
  json: any,
): CompletionStreamInsertChunkDtoData {
  return CompletionStreamInsertChunkDtoDataFromJSONTyped(json, false);
}

export function CompletionStreamInsertChunkDtoDataFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionStreamInsertChunkDtoData {
  if (json == null) {
    return json;
  }
  if (typeof json !== 'object') {
    return json;
  }
  if (instanceOfCompletionBlockDto(json)) {
    return CompletionBlockDtoFromJSONTyped(json, true);
  }
  if (instanceOfCompletionChatDto(json)) {
    return CompletionChatDtoFromJSONTyped(json, true);
  }
  if (instanceOfCompletionMessageDto(json)) {
    return CompletionMessageDtoFromJSONTyped(json, true);
  }

  return {} as any;
}

export function CompletionStreamInsertChunkDtoDataToJSON(json: any): any {
  return CompletionStreamInsertChunkDtoDataToJSONTyped(json, false);
}

export function CompletionStreamInsertChunkDtoDataToJSONTyped(
  value?: CompletionStreamInsertChunkDtoData | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }
  if (typeof value !== 'object') {
    return value;
  }
  if (instanceOfCompletionBlockDto(value)) {
    return CompletionBlockDtoToJSON(value as CompletionBlockDto);
  }
  if (instanceOfCompletionChatDto(value)) {
    return CompletionChatDtoToJSON(value as CompletionChatDto);
  }
  if (instanceOfCompletionMessageDto(value)) {
    return CompletionMessageDtoToJSON(value as CompletionMessageDto);
  }

  return {};
}
