/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { StreamDataTypeEnum } from './StreamDataTypeEnum';
import {
  StreamDataTypeEnumFromJSON,
  StreamDataTypeEnumFromJSONTyped,
  StreamDataTypeEnumToJSON,
  StreamDataTypeEnumToJSONTyped,
} from './StreamDataTypeEnum';

/**
 *
 * @export
 * @interface StreamChunkedDataDto
 */
export interface StreamChunkedDataDto {
  /**
   * The type of stream message
   * @type {StreamDataTypeEnum}
   * @memberof StreamChunkedDataDto
   */
  type: StreamDataTypeEnum;
  /**
   * The type of parent data being updated
   * @type {string}
   * @memberof StreamChunkedDataDto
   */
  parent_data_type: string;
  /**
   * The ID of the parent object being updated
   * @type {string}
   * @memberof StreamChunkedDataDto
   */
  parent_id: string;
  /**
   * The path to the field being updated
   * @type {string}
   * @memberof StreamChunkedDataDto
   */
  chunk_path: string;
  /**
   * The chunk data being updated
   * @type {object}
   * @memberof StreamChunkedDataDto
   */
  chunk_data: object;
}

/**
 * Check if a given object implements the StreamChunkedDataDto interface.
 */
export function instanceOfStreamChunkedDataDto(value: object): value is StreamChunkedDataDto {
  if (!('type' in value) || value.type === undefined) return false;
  if (!('parent_data_type' in value) || value.parent_data_type === undefined) return false;
  if (!('parent_id' in value) || value.parent_id === undefined) return false;
  if (!('chunk_path' in value) || value.chunk_path === undefined) return false;
  if (!('chunk_data' in value) || value.chunk_data === undefined) return false;
  return true;
}

export function StreamChunkedDataDtoFromJSON(json: any): StreamChunkedDataDto {
  return StreamChunkedDataDtoFromJSONTyped(json, false);
}

export function StreamChunkedDataDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamChunkedDataDto {
  if (json == null) {
    return json;
  }
  return {
    type: StreamDataTypeEnumFromJSON(json.type),
    parent_data_type: json.parent_data_type,
    parent_id: json.parent_id,
    chunk_path: json.chunk_path,
    chunk_data: json.chunk_data,
  };
}

export function StreamChunkedDataDtoToJSON(json: any): StreamChunkedDataDto {
  return StreamChunkedDataDtoToJSONTyped(json, false);
}

export function StreamChunkedDataDtoToJSONTyped(
  value?: StreamChunkedDataDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: StreamDataTypeEnumToJSON(value.type),
    parent_data_type: value.parent_data_type,
    parent_id: value.parent_id,
    chunk_path: value.chunk_path,
    chunk_data: value.chunk_data,
  };
}
