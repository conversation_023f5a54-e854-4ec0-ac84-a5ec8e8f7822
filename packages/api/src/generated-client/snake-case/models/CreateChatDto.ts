/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { AtReferenceDto } from './AtReferenceDto';
import {
  AtReferenceDtoFromJSON,
  AtReferenceDtoFromJSONTyped,
  AtReferenceDtoToJSON,
  AtReferenceDtoToJSONTyped,
} from './AtReferenceDto';
import type { ChatOriginDto } from './ChatOriginDto';
import {
  ChatOriginDtoFromJSON,
  ChatOriginDtoFromJSONTyped,
  ChatOriginDtoToJSON,
  ChatOriginDtoToJSONTyped,
} from './ChatOriginDto';
import type { EditCommandDto } from './EditCommandDto';
import {
  EditCommandDtoFromJSON,
  EditCommandDtoFromJSONTyped,
  EditCommandDtoToJSON,
  EditCommandDtoToJSONTyped,
} from './EditCommandDto';
import type { ShortcutDto } from './ShortcutDto';
import {
  ShortcutDtoFromJSON,
  ShortcutDtoFromJSONTyped,
  ShortcutDtoToJSON,
  ShortcutDtoToJSONTyped,
} from './ShortcutDto';
import type { UseToolsDto } from './UseToolsDto';
import {
  UseToolsDtoFromJSON,
  UseToolsDtoFromJSONTyped,
  UseToolsDtoToJSON,
  UseToolsDtoToJSONTyped,
} from './UseToolsDto';

/**
 *
 * @export
 * @interface CreateChatDto
 */
export interface CreateChatDto {
  /**
   * Message content
   * @type {string}
   * @memberof CreateChatDto
   */
  message: string;
  /**
   * Board Id
   * @type {string}
   * @memberof CreateChatDto
   */
  board_id?: string;
  /**
   * Selected text context
   * @type {string}
   * @memberof CreateChatDto
   */
  selection?: string;
  /**
   * Referenced entities
   * @type {Array<AtReferenceDto>}
   * @memberof CreateChatDto
   */
  at_references?: Array<AtReferenceDto>;
  /**
   * AI model to use
   * @type {string}
   * @memberof CreateChatDto
   */
  chat_model?: CreateChatDtoChatModelEnum;
  /**
   * Tools configuration
   * @type {Array<UseToolsDto>}
   * @memberof CreateChatDto
   */
  tools?: Array<UseToolsDto>;
  /**
   * Shortcut information
   * @type {Array<ShortcutDto>}
   * @memberof CreateChatDto
   */
  shortcut?: Array<ShortcutDto>;
  /**
   * Message mode
   * @type {string}
   * @memberof CreateChatDto
   */
  message_mode?: CreateChatDtoMessageModeEnum;
  /**
   * Edit command
   * @type {Array<EditCommandDto>}
   * @memberof CreateChatDto
   */
  command?: Array<EditCommandDto>;
  /**
   * Chat origin context
   * @type {ChatOriginDto}
   * @memberof CreateChatDto
   */
  origin?: ChatOriginDto;
}

/**
 * @export
 * @enum {string}
 */
export enum CreateChatDtoChatModelEnum {
  gpt4o = 'gpt-4o',
  gpt4oMini = 'gpt-4o-mini',
  gpt41 = 'gpt-4.1',
  gpt41Mini = 'gpt-4.1-mini',
  gpt41Nano = 'gpt-4.1-nano',
  o1Mini = 'o1-mini',
  o1Preview = 'o1-preview',
  o3Mini = 'o3-mini',
  o4Mini = 'o4-mini',
  textEmbedding3Large = 'text-embedding-3-large',
  tts1 = 'tts-1',
  tts1Hd = 'tts-1-hd',
  gpt4oMiniTts = 'gpt-4o-mini-tts',
  gptImage1 = 'gpt-image-1',
  claude4Sonnet = 'claude-4-sonnet',
  claude37Sonnet = 'claude-3-7-sonnet',
  claude37SonnetThinking = 'claude-3-7-sonnet-thinking',
  claude35Sonnet = 'claude-3-5-sonnet',
  claude35Haiku = 'claude-3-5-haiku',
  deepseekDeepseekV3 = 'deepseek/deepseek_v3',
  deepseekDeepseekR1 = 'deepseek/deepseek-r1',
  gemini25Pro = 'gemini-2.5-pro',
  gemini25Flash = 'gemini-2.5-flash',
  gemini25FlashLite = 'gemini-2.5-flash-lite',
  gemini20Flash = 'gemini-2.0-flash',
  gemini20FlashLite = 'gemini-2.0-flash-lite',
  qwenPlus = 'qwen-plus',
  qwenMax = 'qwen-max',
  qwenTurbo = 'qwen-turbo',
  speech02Hd = 'speech-02-hd',
  speech02Turbo = 'speech-02-turbo',
  speech01Hd = 'speech-01-hd',
  speech01Turbo = 'speech-01-turbo',
  unknownDefaultOpenApi = '11184809',
}
/**
 * @export
 * @enum {string}
 */
export enum CreateChatDtoMessageModeEnum {
  ask = 'ask',
  agent = 'agent',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the CreateChatDto interface.
 */
export function instanceOfCreateChatDto(value: object): value is CreateChatDto {
  if (!('message' in value) || value.message === undefined) return false;
  return true;
}

export function CreateChatDtoFromJSON(json: any): CreateChatDto {
  return CreateChatDtoFromJSONTyped(json, false);
}

export function CreateChatDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CreateChatDto {
  if (json == null) {
    return json;
  }
  return {
    message: json.message,
    board_id: json.board_id == null ? undefined : json.board_id,
    selection: json.selection == null ? undefined : json.selection,
    at_references:
      json.at_references == null
        ? undefined
        : (json.at_references as Array<any>).map(AtReferenceDtoFromJSON),
    chat_model: json.chat_model == null ? undefined : json.chat_model,
    tools: json.tools == null ? undefined : (json.tools as Array<any>).map(UseToolsDtoFromJSON),
    shortcut:
      json.shortcut == null ? undefined : (json.shortcut as Array<any>).map(ShortcutDtoFromJSON),
    message_mode: json.message_mode == null ? undefined : json.message_mode,
    command:
      json.command == null ? undefined : (json.command as Array<any>).map(EditCommandDtoFromJSON),
    origin: json.origin == null ? undefined : ChatOriginDtoFromJSON(json.origin),
  };
}

export function CreateChatDtoToJSON(json: any): CreateChatDto {
  return CreateChatDtoToJSONTyped(json, false);
}

export function CreateChatDtoToJSONTyped(
  value?: CreateChatDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    message: value.message,
    board_id: value.board_id,
    selection: value.selection,
    at_references:
      value.at_references == null
        ? undefined
        : (value.at_references as Array<any>).map(AtReferenceDtoToJSON),
    chat_model: value.chat_model,
    tools: value.tools == null ? undefined : (value.tools as Array<any>).map(UseToolsDtoToJSON),
    shortcut:
      value.shortcut == null ? undefined : (value.shortcut as Array<any>).map(ShortcutDtoToJSON),
    message_mode: value.message_mode,
    command:
      value.command == null ? undefined : (value.command as Array<any>).map(EditCommandDtoToJSON),
    origin: ChatOriginDtoToJSON(value.origin),
  };
}
