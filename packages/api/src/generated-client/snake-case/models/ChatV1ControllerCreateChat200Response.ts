/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { StreamChunkedDataDto } from './StreamChunkedDataDto';
import {
  instanceOfStreamChunkedDataDto,
  StreamChunkedDataDtoFromJSON,
  StreamChunkedDataDtoFromJSONTyped,
  StreamChunkedDataDtoToJSON,
} from './StreamChunkedDataDto';
import type { StreamDataDto } from './StreamDataDto';
import {
  instanceOfStreamDataDto,
  StreamDataDtoFromJSON,
  StreamDataDtoFromJSONTyped,
  StreamDataDtoToJSON,
} from './StreamDataDto';
import type { StreamErrorDto } from './StreamErrorDto';
import {
  instanceOfStreamErrorDto,
  StreamErrorDtoFromJSON,
  StreamErrorDtoFromJSONTyped,
  StreamErrorDtoToJSON,
} from './StreamErrorDto';
import type { StreamMessageEventDto } from './StreamMessageEventDto';
import {
  instanceOfStreamMessageEventDto,
  StreamMessageEventDtoFromJSON,
  StreamMessageEventDtoFromJSONTyped,
  StreamMessageEventDtoToJSON,
} from './StreamMessageEventDto';
import type { StreamStatusUpdateDto } from './StreamStatusUpdateDto';
import {
  instanceOfStreamStatusUpdateDto,
  StreamStatusUpdateDtoFromJSON,
  StreamStatusUpdateDtoFromJSONTyped,
  StreamStatusUpdateDtoToJSON,
} from './StreamStatusUpdateDto';
import type { StreamTextDto } from './StreamTextDto';
import {
  instanceOfStreamTextDto,
  StreamTextDtoFromJSON,
  StreamTextDtoFromJSONTyped,
  StreamTextDtoToJSON,
} from './StreamTextDto';
import type { StreamTextStartDto } from './StreamTextStartDto';
import {
  instanceOfStreamTextStartDto,
  StreamTextStartDtoFromJSON,
  StreamTextStartDtoFromJSONTyped,
  StreamTextStartDtoToJSON,
} from './StreamTextStartDto';
import type { StreamTextStopDto } from './StreamTextStopDto';
import {
  instanceOfStreamTextStopDto,
  StreamTextStopDtoFromJSON,
  StreamTextStopDtoFromJSONTyped,
  StreamTextStopDtoToJSON,
} from './StreamTextStopDto';
import type { StreamTraceDto } from './StreamTraceDto';
import {
  instanceOfStreamTraceDto,
  StreamTraceDtoFromJSON,
  StreamTraceDtoFromJSONTyped,
  StreamTraceDtoToJSON,
} from './StreamTraceDto';

/**
 * @type ChatV1ControllerCreateChat200Response
 *
 * @export
 */
export type ChatV1ControllerCreateChat200Response =
  | StreamChunkedDataDto
  | StreamDataDto
  | StreamErrorDto
  | StreamMessageEventDto
  | StreamStatusUpdateDto
  | StreamTextDto
  | StreamTextStartDto
  | StreamTextStopDto
  | StreamTraceDto;

export function ChatV1ControllerCreateChat200ResponseFromJSON(
  json: any,
): ChatV1ControllerCreateChat200Response {
  return ChatV1ControllerCreateChat200ResponseFromJSONTyped(json, false);
}

export function ChatV1ControllerCreateChat200ResponseFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ChatV1ControllerCreateChat200Response {
  if (json == null) {
    return json;
  }
  if (typeof json !== 'object') {
    return json;
  }
  if (instanceOfStreamChunkedDataDto(json)) {
    return StreamChunkedDataDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamDataDto(json)) {
    return StreamDataDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamErrorDto(json)) {
    return StreamErrorDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamMessageEventDto(json)) {
    return StreamMessageEventDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamStatusUpdateDto(json)) {
    return StreamStatusUpdateDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamTextDto(json)) {
    return StreamTextDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamTextStartDto(json)) {
    return StreamTextStartDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamTextStopDto(json)) {
    return StreamTextStopDtoFromJSONTyped(json, true);
  }
  if (instanceOfStreamTraceDto(json)) {
    return StreamTraceDtoFromJSONTyped(json, true);
  }

  return {} as any;
}

export function ChatV1ControllerCreateChat200ResponseToJSON(json: any): any {
  return ChatV1ControllerCreateChat200ResponseToJSONTyped(json, false);
}

export function ChatV1ControllerCreateChat200ResponseToJSONTyped(
  value?: ChatV1ControllerCreateChat200Response | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }
  if (typeof value !== 'object') {
    return value;
  }
  if (instanceOfStreamChunkedDataDto(value)) {
    return StreamChunkedDataDtoToJSON(value as StreamChunkedDataDto);
  }
  if (instanceOfStreamDataDto(value)) {
    return StreamDataDtoToJSON(value as StreamDataDto);
  }
  if (instanceOfStreamErrorDto(value)) {
    return StreamErrorDtoToJSON(value as StreamErrorDto);
  }
  if (instanceOfStreamMessageEventDto(value)) {
    return StreamMessageEventDtoToJSON(value as StreamMessageEventDto);
  }
  if (instanceOfStreamStatusUpdateDto(value)) {
    return StreamStatusUpdateDtoToJSON(value as StreamStatusUpdateDto);
  }
  if (instanceOfStreamTextDto(value)) {
    return StreamTextDtoToJSON(value as StreamTextDto);
  }
  if (instanceOfStreamTextStartDto(value)) {
    return StreamTextStartDtoToJSON(value as StreamTextStartDto);
  }
  if (instanceOfStreamTextStopDto(value)) {
    return StreamTextStopDtoToJSON(value as StreamTextStopDto);
  }
  if (instanceOfStreamTraceDto(value)) {
    return StreamTraceDtoToJSON(value as StreamTraceDto);
  }

  return {};
}
