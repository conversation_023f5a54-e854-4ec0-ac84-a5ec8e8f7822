/* tslint:disable */
/* eslint-disable */
/**
 * YouAP<PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface CompletionChatDto
 */
export interface CompletionChatDto {
  /**
   * The ID of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof CompletionChatDto
   */
  creator_id: string;
  /**
   * The creation time of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  created_at: string;
  /**
   * The update time of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  updated_at: string;
  /**
   * The mode of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  mode: string;
  /**
   * The title of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  title: string;
  /**
   * The origin of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  origin: string;
  /**
   * The board ID of the chat
   * @type {string}
   * @memberof CompletionChatDto
   */
  board_id?: string;
}

/**
 * Check if a given object implements the CompletionChatDto interface.
 */
export function instanceOfCompletionChatDto(value: object): value is CompletionChatDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('creator_id' in value) || value.creator_id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('mode' in value) || value.mode === undefined) return false;
  if (!('title' in value) || value.title === undefined) return false;
  if (!('origin' in value) || value.origin === undefined) return false;
  return true;
}

export function CompletionChatDtoFromJSON(json: any): CompletionChatDto {
  return CompletionChatDtoFromJSONTyped(json, false);
}

export function CompletionChatDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionChatDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    creator_id: json.creator_id,
    created_at: json.created_at,
    updated_at: json.updated_at,
    mode: json.mode,
    title: json.title,
    origin: json.origin,
    board_id: json.board_id == null ? undefined : json.board_id,
  };
}

export function CompletionChatDtoToJSON(json: any): CompletionChatDto {
  return CompletionChatDtoToJSONTyped(json, false);
}

export function CompletionChatDtoToJSONTyped(
  value?: CompletionChatDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    creator_id: value.creator_id,
    created_at: value.created_at,
    updated_at: value.updated_at,
    mode: value.mode,
    title: value.title,
    origin: value.origin,
    board_id: value.board_id,
  };
}
