/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { ImageGenerateToolDto } from './ImageGenerateToolDto';
import {
  ImageGenerateToolDtoFromJSON,
  ImageGenerateToolDtoFromJSONTyped,
  ImageGenerateToolDtoToJSON,
  ImageGenerateToolDtoToJSONTyped,
} from './ImageGenerateToolDto';
import type { ToolOptionsDto } from './ToolOptionsDto';
import {
  ToolOptionsDtoFromJSON,
  ToolOptionsDtoFromJSONTyped,
  ToolOptionsDtoToJSON,
  ToolOptionsDtoToJSONTyped,
} from './ToolOptionsDto';

/**
 *
 * @export
 * @interface UseToolsDto
 */
export interface UseToolsDto {
  /**
   * Image generation tool options
   * @type {ImageGenerateToolDto}
   * @memberof UseToolsDto
   */
  image_generate?: ImageGenerateToolDto;
  /**
   * Diagram generation tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  diagram_generate?: ToolOptionsDto;
  /**
   * Google search tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  google_search?: ToolOptionsDto;
  /**
   * Library search tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  library_search?: ToolOptionsDto;
  /**
   * Edit thought tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  edit_thought?: ToolOptionsDto;
  /**
   * Audio generation tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  audio_generate?: ToolOptionsDto;
  /**
   * Board search tool options
   * @type {ToolOptionsDto}
   * @memberof UseToolsDto
   */
  board_search?: ToolOptionsDto;
}

/**
 * Check if a given object implements the UseToolsDto interface.
 */
export function instanceOfUseToolsDto(value: object): value is UseToolsDto {
  return true;
}

export function UseToolsDtoFromJSON(json: any): UseToolsDto {
  return UseToolsDtoFromJSONTyped(json, false);
}

export function UseToolsDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): UseToolsDto {
  if (json == null) {
    return json;
  }
  return {
    image_generate:
      json.image_generate == null ? undefined : ImageGenerateToolDtoFromJSON(json.image_generate),
    diagram_generate:
      json.diagram_generate == null ? undefined : ToolOptionsDtoFromJSON(json.diagram_generate),
    google_search:
      json.google_search == null ? undefined : ToolOptionsDtoFromJSON(json.google_search),
    library_search:
      json.library_search == null ? undefined : ToolOptionsDtoFromJSON(json.library_search),
    edit_thought: json.edit_thought == null ? undefined : ToolOptionsDtoFromJSON(json.edit_thought),
    audio_generate:
      json.audio_generate == null ? undefined : ToolOptionsDtoFromJSON(json.audio_generate),
    board_search: json.board_search == null ? undefined : ToolOptionsDtoFromJSON(json.board_search),
  };
}

export function UseToolsDtoToJSON(json: any): UseToolsDto {
  return UseToolsDtoToJSONTyped(json, false);
}

export function UseToolsDtoToJSONTyped(
  value?: UseToolsDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    image_generate: ImageGenerateToolDtoToJSON(value.image_generate),
    diagram_generate: ToolOptionsDtoToJSON(value.diagram_generate),
    google_search: ToolOptionsDtoToJSON(value.google_search),
    library_search: ToolOptionsDtoToJSON(value.library_search),
    edit_thought: ToolOptionsDtoToJSON(value.edit_thought),
    audio_generate: ToolOptionsDtoToJSON(value.audio_generate),
    board_search: ToolOptionsDtoToJSON(value.board_search),
  };
}
