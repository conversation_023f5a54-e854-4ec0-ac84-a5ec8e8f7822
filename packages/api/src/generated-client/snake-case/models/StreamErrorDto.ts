/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { StreamDataTypeEnum } from './StreamDataTypeEnum';
import {
  StreamDataTypeEnumFromJSON,
  StreamDataTypeEnumFromJSONTyped,
  StreamDataTypeEnumToJSON,
  StreamDataTypeEnumToJSONTyped,
} from './StreamDataTypeEnum';

/**
 *
 * @export
 * @interface StreamErrorDto
 */
export interface StreamErrorDto {
  /**
   * The type of stream message
   * @type {StreamDataTypeEnum}
   * @memberof StreamErrorDto
   */
  type: StreamDataTypeEnum;
  /**
   * Error information
   * @type {object}
   * @memberof StreamErrorDto
   */
  error: object;
}

/**
 * Check if a given object implements the StreamErrorDto interface.
 */
export function instanceOfStreamErrorDto(value: object): value is StreamErrorDto {
  if (!('type' in value) || value.type === undefined) return false;
  if (!('error' in value) || value.error === undefined) return false;
  return true;
}

export function StreamErrorDtoFromJSON(json: any): StreamErrorDto {
  return StreamErrorDtoFromJSONTyped(json, false);
}

export function StreamErrorDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamErrorDto {
  if (json == null) {
    return json;
  }
  return {
    type: StreamDataTypeEnumFromJSON(json.type),
    error: json.error,
  };
}

export function StreamErrorDtoToJSON(json: any): StreamErrorDto {
  return StreamErrorDtoToJSONTyped(json, false);
}

export function StreamErrorDtoToJSONTyped(
  value?: StreamErrorDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: StreamDataTypeEnumToJSON(value.type),
    error: value.error,
  };
}
