/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CompletionStreamModeEnum } from './CompletionStreamModeEnum';
import {
  CompletionStreamModeEnumFromJSON,
  CompletionStreamModeEnumFromJSONTyped,
  CompletionStreamModeEnumToJSON,
  CompletionStreamModeEnumToJSONTyped,
} from './CompletionStreamModeEnum';

/**
 *
 * @export
 * @interface CompletionStreamAppendStringChunkDto
 */
export interface CompletionStreamAppendStringChunkDto {
  /**
   * The mode of the stream chunk operation
   * @type {CompletionStreamModeEnum}
   * @memberof CompletionStreamAppendStringChunkDto
   */
  mode: CompletionStreamModeEnum;
  /**
   * The type of target being appended to
   * @type {string}
   * @memberof CompletionStreamAppendStringChunkDto
   */
  target_type: CompletionStreamAppendStringChunkDtoTargetTypeEnum;
  /**
   * The ID of the target being appended to
   * @type {string}
   * @memberof CompletionStreamAppendStringChunkDto
   */
  target_id: string;
  /**
   * The string data to append
   * @type {string}
   * @memberof CompletionStreamAppendStringChunkDto
   */
  data: string;
  /**
   * The path of the field being appended to
   * @type {string}
   * @memberof CompletionStreamAppendStringChunkDto
   */
  path: string;
}

/**
 * @export
 * @enum {string}
 */
export enum CompletionStreamAppendStringChunkDtoTargetTypeEnum {
  chat = 'Chat',
  message = 'Message',
  completionBlock = 'CompletionBlock',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the CompletionStreamAppendStringChunkDto interface.
 */
export function instanceOfCompletionStreamAppendStringChunkDto(
  value: object,
): value is CompletionStreamAppendStringChunkDto {
  if (!('mode' in value) || value.mode === undefined) return false;
  if (!('target_type' in value) || value.target_type === undefined) return false;
  if (!('target_id' in value) || value.target_id === undefined) return false;
  if (!('data' in value) || value.data === undefined) return false;
  if (!('path' in value) || value.path === undefined) return false;
  return true;
}

export function CompletionStreamAppendStringChunkDtoFromJSON(
  json: any,
): CompletionStreamAppendStringChunkDto {
  return CompletionStreamAppendStringChunkDtoFromJSONTyped(json, false);
}

export function CompletionStreamAppendStringChunkDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionStreamAppendStringChunkDto {
  if (json == null) {
    return json;
  }
  return {
    mode: CompletionStreamModeEnumFromJSON(json.mode),
    target_type: json.target_type,
    target_id: json.target_id,
    data: json.data,
    path: json.path,
  };
}

export function CompletionStreamAppendStringChunkDtoToJSON(
  json: any,
): CompletionStreamAppendStringChunkDto {
  return CompletionStreamAppendStringChunkDtoToJSONTyped(json, false);
}

export function CompletionStreamAppendStringChunkDtoToJSONTyped(
  value?: CompletionStreamAppendStringChunkDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    mode: CompletionStreamModeEnumToJSON(value.mode),
    target_type: value.target_type,
    target_id: value.target_id,
    data: value.data,
    path: value.path,
  };
}
