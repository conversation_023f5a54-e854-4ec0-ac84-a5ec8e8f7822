/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface EditCommandDto
 */
export interface EditCommandDto {
  /**
   * Command type
   * @type {string}
   * @memberof EditCommandDto
   */
  type: EditCommandDtoTypeEnum;
  /**
   * Direction for adjust length command
   * @type {string}
   * @memberof EditCommandDto
   */
  direction?: EditCommandDtoDirectionEnum;
  /**
   * Target language for translation
   * @type {string}
   * @memberof EditCommandDto
   */
  target_language?: EditCommandDtoTargetLanguageEnum;
  /**
   * Translation mode
   * @type {string}
   * @memberof EditCommandDto
   */
  mode?: EditCommandDtoModeEnum;
}

/**
 * @export
 * @enum {string}
 */
export enum EditCommandDtoTypeEnum {
  suggestEdits = 'suggest_edits',
  suggestSearch = 'suggest_search',
  suggestSummary = 'suggest_summary',
  suggestNextStep = 'suggest_next_step',
  suggestGenerateText = 'suggest_generate_text',
  suggestGenerateImage = 'suggest_generate_image',
  suggestGenerateAudio = 'suggest_generate_audio',
  autoIllustration = 'auto_illustration',
  adjustLength = 'adjust_length',
  translate = 'translate',
  unknownDefaultOpenApi = '11184809',
}
/**
 * @export
 * @enum {string}
 */
export enum EditCommandDtoDirectionEnum {
  increase = 'increase',
  decrease = 'decrease',
  unknownDefaultOpenApi = '11184809',
}
/**
 * @export
 * @enum {string}
 */
export enum EditCommandDtoTargetLanguageEnum {
  am = 'am',
  ar = 'ar',
  bgBg = 'bg-BG',
  bnBd = 'bn-BD',
  bsBa = 'bs-BA',
  caEs = 'ca-ES',
  csCz = 'cs-CZ',
  daDk = 'da-DK',
  deDe = 'de-DE',
  elGr = 'el-GR',
  enUs = 'en-US',
  es419 = 'es-419',
  esEs = 'es-ES',
  etEe = 'et-EE',
  fiFi = 'fi-FI',
  frCa = 'fr-CA',
  frFr = 'fr-FR',
  guIn = 'gu-IN',
  hiIn = 'hi-IN',
  hrHr = 'hr-HR',
  huHu = 'hu-HU',
  hyAm = 'hy-AM',
  idId = 'id-ID',
  isIs = 'is-IS',
  itIt = 'it-IT',
  jaJp = 'ja-JP',
  kaGe = 'ka-GE',
  kk = 'kk',
  knIn = 'kn-IN',
  koKr = 'ko-KR',
  lt = 'lt',
  lvLv = 'lv-LV',
  mkMk = 'mk-MK',
  ml = 'ml',
  mn = 'mn',
  mrIn = 'mr-IN',
  msMy = 'ms-MY',
  myMm = 'my-MM',
  nbNo = 'nb-NO',
  nlNl = 'nl-NL',
  pa = 'pa',
  plPl = 'pl-PL',
  ptBr = 'pt-BR',
  ptPt = 'pt-PT',
  roRo = 'ro-RO',
  ruRu = 'ru-RU',
  skSk = 'sk-SK',
  slSi = 'sl-SI',
  soSo = 'so-SO',
  sqAl = 'sq-AL',
  srRs = 'sr-RS',
  svSe = 'sv-SE',
  swTz = 'sw-TZ',
  taIn = 'ta-IN',
  teIn = 'te-IN',
  thTh = 'th-TH',
  tl = 'tl',
  trTr = 'tr-TR',
  ukUa = 'uk-UA',
  ur = 'ur',
  viVn = 'vi-VN',
  zhCn = 'zh-CN',
  zhHk = 'zh-HK',
  zhTw = 'zh-TW',
  unknownDefaultOpenApi = '11184809',
}
/**
 * @export
 * @enum {string}
 */
export enum EditCommandDtoModeEnum {
  replace = 'replace',
  insert = 'insert',
  append = 'append',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the EditCommandDto interface.
 */
export function instanceOfEditCommandDto(value: object): value is EditCommandDto {
  if (!('type' in value) || value.type === undefined) return false;
  return true;
}

export function EditCommandDtoFromJSON(json: any): EditCommandDto {
  return EditCommandDtoFromJSONTyped(json, false);
}

export function EditCommandDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): EditCommandDto {
  if (json == null) {
    return json;
  }
  return {
    type: json.type,
    direction: json.direction == null ? undefined : json.direction,
    target_language: json.target_language == null ? undefined : json.target_language,
    mode: json.mode == null ? undefined : json.mode,
  };
}

export function EditCommandDtoToJSON(json: any): EditCommandDto {
  return EditCommandDtoToJSONTyped(json, false);
}

export function EditCommandDtoToJSONTyped(
  value?: EditCommandDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: value.type,
    direction: value.direction,
    target_language: value.target_language,
    mode: value.mode,
  };
}
