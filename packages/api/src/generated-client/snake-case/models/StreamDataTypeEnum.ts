/* tslint:disable */
/* eslint-disable */
/**
 * YouAP<PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * The type of stream message
 * @export
 * @enum {string}
 */
export enum StreamDataTypeEnum {
  data = 'data',
  chunkedData = 'chunked_data',
  error = 'error',
  content = 'content',
  contentStop = 'content_stop',
  contentStart = 'content_start',
  statusUpdate = 'status_update',
  traceId = 'trace_id',
  unknownDefaultOpenApi = '11184809',
}

export function instanceOfStreamDataTypeEnum(value: any): boolean {
  for (const key in StreamDataTypeEnum) {
    if (Object.hasOwn(StreamDataTypeEnum, key)) {
      if (StreamDataTypeEnum[key as keyof typeof StreamDataTypeEnum] === value) {
        return true;
      }
    }
  }
  return false;
}

export function StreamDataTypeEnumFromJSON(json: any): StreamDataTypeEnum {
  return StreamDataTypeEnumFromJSONTyped(json, false);
}

export function StreamDataTypeEnumFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): StreamDataTypeEnum {
  return json as StreamDataTypeEnum;
}

export function StreamDataTypeEnumToJSON(value?: StreamDataTypeEnum | null): any {
  return value as any;
}

export function StreamDataTypeEnumToJSONTyped(
  value: any,
  _ignoreDiscriminator: boolean,
): StreamDataTypeEnum {
  return value as StreamDataTypeEnum;
}
