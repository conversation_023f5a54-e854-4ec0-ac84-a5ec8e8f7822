/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CompletionStreamEventDtoData } from './CompletionStreamEventDtoData';
import {
  CompletionStreamEventDtoDataFromJSON,
  CompletionStreamEventDtoDataFromJSONTyped,
  CompletionStreamEventDtoDataToJSON,
  CompletionStreamEventDtoDataToJSONTyped,
} from './CompletionStreamEventDtoData';

/**
 *
 * @export
 * @interface CompletionStreamEventDto
 */
export interface CompletionStreamEventDto {
  /**
   * Event ID for SSE
   * @type {string}
   * @memberof CompletionStreamEventDto
   */
  id?: string;
  /**
   * Event type for SSE
   * @type {string}
   * @memberof CompletionStreamEventDto
   */
  event?: string;
  /**
   *
   * @type {CompletionStreamEventDtoData}
   * @memberof CompletionStreamEventDto
   */
  data: CompletionStreamEventDtoData;
}

/**
 * Check if a given object implements the CompletionStreamEventDto interface.
 */
export function instanceOfCompletionStreamEventDto(
  value: object,
): value is CompletionStreamEventDto {
  if (!('data' in value) || value.data === undefined) return false;
  return true;
}

export function CompletionStreamEventDtoFromJSON(json: any): CompletionStreamEventDto {
  return CompletionStreamEventDtoFromJSONTyped(json, false);
}

export function CompletionStreamEventDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): CompletionStreamEventDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id == null ? undefined : json.id,
    event: json.event == null ? undefined : json.event,
    data: CompletionStreamEventDtoDataFromJSON(json.data),
  };
}

export function CompletionStreamEventDtoToJSON(json: any): CompletionStreamEventDto {
  return CompletionStreamEventDtoToJSONTyped(json, false);
}

export function CompletionStreamEventDtoToJSONTyped(
  value?: CompletionStreamEventDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    event: value.event,
    data: CompletionStreamEventDtoDataToJSON(value.data),
  };
}
