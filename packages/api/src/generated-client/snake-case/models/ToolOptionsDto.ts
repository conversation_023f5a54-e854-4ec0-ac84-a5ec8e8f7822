/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface ToolOptionsDto
 */
export interface ToolOptionsDto {
  /**
   * Tool use option
   * @type {string}
   * @memberof ToolOptionsDto
   */
  use_tool: ToolOptionsDtoUseToolEnum;
}

/**
 * @export
 * @enum {string}
 */
export enum ToolOptionsDtoUseToolEnum {
  required = 'required',
  auto = 'auto',
  none = 'none',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the ToolOptionsDto interface.
 */
export function instanceOfToolOptionsDto(value: object): value is ToolOptionsDto {
  if (!('use_tool' in value) || value.use_tool === undefined) return false;
  return true;
}

export function ToolOptionsDtoFromJSON(json: any): ToolOptionsDto {
  return ToolOptionsDtoFromJSONTyped(json, false);
}

export function ToolOptionsDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ToolOptionsDto {
  if (json == null) {
    return json;
  }
  return {
    use_tool: json.use_tool,
  };
}

export function ToolOptionsDtoToJSON(json: any): ToolOptionsDto {
  return ToolOptionsDtoToJSONTyped(json, false);
}

export function ToolOptionsDtoToJSONTyped(
  value?: ToolOptionsDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    use_tool: value.use_tool,
  };
}
