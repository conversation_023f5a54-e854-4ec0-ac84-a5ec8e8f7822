/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type {
  ChatDetailV2Dto,
  ChatIdDto,
  Chat<PERSON>riginDto,
  ChatV2ControllerCreateChat200Response,
  CreateChatDto,
  CreateEmptyChatDto,
  ListChatHistoryV2Dto,
  ListChatHistoryV2ResponseDto,
  RegenerateMessageV2Dto,
  SendMessageDto,
} from '../models/index';
import {
  ChatDetailV2DtoFromJSON,
  ChatDetailV2DtoToJSON,
  ChatIdDtoFromJSON,
  ChatIdDtoToJSON,
  Chat<PERSON><PERSON>inDtoFromJSON,
  Chat<PERSON><PERSON><PERSON><PERSON>toToJSON,
  ChatV2ControllerCreateChat200ResponseFromJSON,
  ChatV2ControllerCreateChat200ResponseToJSON,
  CreateChatDtoFromJSON,
  CreateChatDtoToJSON,
  CreateEmptyChatDtoFromJSON,
  CreateEmptyChatDtoToJSON,
  ListChatHistoryV2DtoFromJSON,
  ListChatHistoryV2DtoToJSON,
  ListChatHistoryV2ResponseDtoFromJSON,
  ListChatHistoryV2ResponseDtoToJSON,
  RegenerateMessageV2DtoFromJSON,
  RegenerateMessageV2DtoToJSON,
  SendMessageDtoFromJSON,
  SendMessageDtoToJSON,
} from '../models/index';

export interface ChatV2ControllerCreateChatRequest {
  createChatDto: CreateChatDto;
}

export interface ChatV2ControllerCreateEmptyChatRequest {
  createEmptyChatDto: CreateEmptyChatDto;
}

export interface ChatV2ControllerDeleteChatRequest {
  chatIdDto: ChatIdDto;
}

export interface ChatV2ControllerGetChatDetailRequest {
  chatIdDto: ChatIdDto;
}

export interface ChatV2ControllerGetChatDetailByOriginRequest {
  chatOriginDto: ChatOriginDto;
}

export interface ChatV2ControllerListChatHistoryRequest {
  listChatHistoryV2Dto: ListChatHistoryV2Dto;
}

export interface ChatV2ControllerRegenerateMessageRequest {
  regenerateMessageV2Dto: RegenerateMessageV2Dto;
}

export interface ChatV2ControllerSendMessageRequest {
  sendMessageDto: SendMessageDto;
}

/**
 * ChatV2Api - interface
 *
 * @export
 * @interface ChatV2ApiInterface
 */
export interface ChatV2ApiInterface {
  /**
   * Creates a new chat and sends a message, returning a server-sent event stream with completion chunks
   * @summary Create chat and send message
   * @param {CreateChatDto} createChatDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV2ApiInterface
   */
  createChatRaw(
    requestParameters: ChatV2ControllerCreateChatRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV2ControllerCreateChat200Response>>;

  /**
   * Creates a new chat and sends a message, returning a server-sent event stream with completion chunks
   * Create chat and send message
   */
  createChat(
    createChatDto: CreateChatDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV2ControllerCreateChat200Response>;

  /**
   * Creates a new empty chat without any messages, suitable for assistant initialization
   * @summary Create empty chat for assistant
   * @param {CreateEmptyChatDto} createEmptyChatDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV2ApiInterface
   */
  createEmptyChatRaw(
    requestParameters: ChatV2ControllerCreateEmptyChatRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailV2Dto>>;

  /**
   * Creates a new empty chat without any messages, suitable for assistant initialization
   * Create empty chat for assistant
   */
  createEmptyChat(
    createEmptyChatDto: CreateEmptyChatDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailV2Dto>;

  /**
   *
   * @summary Delete Chat
   * @param {ChatIdDto} chatIdDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV2ApiInterface
   */
  deleteChatRaw(
    requestParameters: ChatV2ControllerDeleteChatRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * Delete Chat
   */
  deleteChat(
    chatIdDto: ChatIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   *
   * @summary Get chat details (V2 format)
   * @param {ChatIdDto} chatIdDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV2ApiInterface
   */
  getChatDetailRaw(
    requestParameters: ChatV2ControllerGetChatDetailRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailV2Dto>>;

  /**
   * Get chat details (V2 format)
   */
  getChatDetail(
    chatIdDto: ChatIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailV2Dto>;

  /**
   * 根据来源查询聊天详情，返回最相关的单个聊天详情，用于Chat Assistant默认展示
   * @summary Get chat details by origin (V2 format)
   * @param {ChatOriginDto} chatOriginDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV2ApiInterface
   */
  getChatDetailByOriginRaw(
    requestParameters: ChatV2ControllerGetChatDetailByOriginRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailV2Dto>>;

  /**
   * 根据来源查询聊天详情，返回最相关的单个聊天详情，用于Chat Assistant默认展示
   * Get chat details by origin (V2 format)
   */
  getChatDetailByOrigin(
    chatOriginDto: ChatOriginDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailV2Dto>;

  /**
   *
   * @summary List chat history (V2 format)
   * @param {ListChatHistoryV2Dto} listChatHistoryV2Dto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV2ApiInterface
   */
  listChatHistoryRaw(
    requestParameters: ChatV2ControllerListChatHistoryRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ListChatHistoryV2ResponseDto>>;

  /**
   * List chat history (V2 format)
   */
  listChatHistory(
    listChatHistoryV2Dto: ListChatHistoryV2Dto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ListChatHistoryV2ResponseDto>;

  /**
   * Regenerates an AI assistant message and returns a server-sent event stream with the new response
   * @summary Regenerate message (V2 format)
   * @param {RegenerateMessageV2Dto} regenerateMessageV2Dto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV2ApiInterface
   */
  regenerateMessageRaw(
    requestParameters: ChatV2ControllerRegenerateMessageRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV2ControllerCreateChat200Response>>;

  /**
   * Regenerates an AI assistant message and returns a server-sent event stream with the new response
   * Regenerate message (V2 format)
   */
  regenerateMessage(
    regenerateMessageV2Dto: RegenerateMessageV2Dto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV2ControllerCreateChat200Response>;

  /**
   * Sends a message to an existing chat and returns a server-sent event stream with AI response chunks
   * @summary Send a message to an existing chat
   * @param {SendMessageDto} sendMessageDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ChatV2ApiInterface
   */
  sendMessageRaw(
    requestParameters: ChatV2ControllerSendMessageRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV2ControllerCreateChat200Response>>;

  /**
   * Sends a message to an existing chat and returns a server-sent event stream with AI response chunks
   * Send a message to an existing chat
   */
  sendMessage(
    sendMessageDto: SendMessageDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV2ControllerCreateChat200Response>;
}

/**
 *
 */
export class ChatV2Api extends runtime.BaseAPI implements ChatV2ApiInterface {
  /**
   * Creates a new chat and sends a message, returning a server-sent event stream with completion chunks
   * Create chat and send message
   */
  async createChatRaw(
    requestParameters: ChatV2ControllerCreateChatRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV2ControllerCreateChat200Response>> {
    if (requestParameters.createChatDto == null) {
      throw new runtime.RequiredError(
        'createChatDto',
        'Required parameter "createChatDto" was null or undefined when calling createChat().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v2/chatAssistant/createChat`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: CreateChatDtoToJSON(requestParameters.createChatDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ChatV2ControllerCreateChat200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Creates a new chat and sends a message, returning a server-sent event stream with completion chunks
   * Create chat and send message
   */
  async createChat(
    createChatDto: CreateChatDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV2ControllerCreateChat200Response> {
    const response = await this.createChatRaw({ createChatDto: createChatDto }, initOverrides);
    return await response.value();
  }

  /**
   * Creates a new empty chat without any messages, suitable for assistant initialization
   * Create empty chat for assistant
   */
  async createEmptyChatRaw(
    requestParameters: ChatV2ControllerCreateEmptyChatRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailV2Dto>> {
    if (requestParameters.createEmptyChatDto == null) {
      throw new runtime.RequiredError(
        'createEmptyChatDto',
        'Required parameter "createEmptyChatDto" was null or undefined when calling createEmptyChat().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v2/chatAssistant/createEmptyChat`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: CreateEmptyChatDtoToJSON(requestParameters.createEmptyChatDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => ChatDetailV2DtoFromJSON(jsonValue));
  }

  /**
   * Creates a new empty chat without any messages, suitable for assistant initialization
   * Create empty chat for assistant
   */
  async createEmptyChat(
    createEmptyChatDto: CreateEmptyChatDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailV2Dto> {
    const response = await this.createEmptyChatRaw(
      { createEmptyChatDto: createEmptyChatDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * Delete Chat
   */
  async deleteChatRaw(
    requestParameters: ChatV2ControllerDeleteChatRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.chatIdDto == null) {
      throw new runtime.RequiredError(
        'chatIdDto',
        'Required parameter "chatIdDto" was null or undefined when calling deleteChat().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v2/chatAssistant/deleteChat`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ChatIdDtoToJSON(requestParameters.chatIdDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * Delete Chat
   */
  async deleteChat(
    chatIdDto: ChatIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.deleteChatRaw({ chatIdDto: chatIdDto }, initOverrides);
  }

  /**
   * Get chat details (V2 format)
   */
  async getChatDetailRaw(
    requestParameters: ChatV2ControllerGetChatDetailRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailV2Dto>> {
    if (requestParameters.chatIdDto == null) {
      throw new runtime.RequiredError(
        'chatIdDto',
        'Required parameter "chatIdDto" was null or undefined when calling getChatDetail().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v2/chatAssistant/getChatDetail`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ChatIdDtoToJSON(requestParameters.chatIdDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => ChatDetailV2DtoFromJSON(jsonValue));
  }

  /**
   * Get chat details (V2 format)
   */
  async getChatDetail(
    chatIdDto: ChatIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailV2Dto> {
    const response = await this.getChatDetailRaw({ chatIdDto: chatIdDto }, initOverrides);
    return await response.value();
  }

  /**
   * 根据来源查询聊天详情，返回最相关的单个聊天详情，用于Chat Assistant默认展示
   * Get chat details by origin (V2 format)
   */
  async getChatDetailByOriginRaw(
    requestParameters: ChatV2ControllerGetChatDetailByOriginRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatDetailV2Dto>> {
    if (requestParameters.chatOriginDto == null) {
      throw new runtime.RequiredError(
        'chatOriginDto',
        'Required parameter "chatOriginDto" was null or undefined when calling getChatDetailByOrigin().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v2/chatAssistant/getChatDetailByOrigin`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ChatOriginDtoToJSON(requestParameters.chatOriginDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => ChatDetailV2DtoFromJSON(jsonValue));
  }

  /**
   * 根据来源查询聊天详情，返回最相关的单个聊天详情，用于Chat Assistant默认展示
   * Get chat details by origin (V2 format)
   */
  async getChatDetailByOrigin(
    chatOriginDto: ChatOriginDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatDetailV2Dto> {
    const response = await this.getChatDetailByOriginRaw(
      { chatOriginDto: chatOriginDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * List chat history (V2 format)
   */
  async listChatHistoryRaw(
    requestParameters: ChatV2ControllerListChatHistoryRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ListChatHistoryV2ResponseDto>> {
    if (requestParameters.listChatHistoryV2Dto == null) {
      throw new runtime.RequiredError(
        'listChatHistoryV2Dto',
        'Required parameter "listChatHistoryV2Dto" was null or undefined when calling listChatHistory().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v2/chatAssistant/listChatHistory`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ListChatHistoryV2DtoToJSON(requestParameters.listChatHistoryV2Dto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ListChatHistoryV2ResponseDtoFromJSON(jsonValue),
    );
  }

  /**
   * List chat history (V2 format)
   */
  async listChatHistory(
    listChatHistoryV2Dto: ListChatHistoryV2Dto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ListChatHistoryV2ResponseDto> {
    const response = await this.listChatHistoryRaw(
      { listChatHistoryV2Dto: listChatHistoryV2Dto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * Regenerates an AI assistant message and returns a server-sent event stream with the new response
   * Regenerate message (V2 format)
   */
  async regenerateMessageRaw(
    requestParameters: ChatV2ControllerRegenerateMessageRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV2ControllerCreateChat200Response>> {
    if (requestParameters.regenerateMessageV2Dto == null) {
      throw new runtime.RequiredError(
        'regenerateMessageV2Dto',
        'Required parameter "regenerateMessageV2Dto" was null or undefined when calling regenerateMessage().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v2/chatAssistant/regenerateMessage`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: RegenerateMessageV2DtoToJSON(requestParameters.regenerateMessageV2Dto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ChatV2ControllerCreateChat200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Regenerates an AI assistant message and returns a server-sent event stream with the new response
   * Regenerate message (V2 format)
   */
  async regenerateMessage(
    regenerateMessageV2Dto: RegenerateMessageV2Dto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV2ControllerCreateChat200Response> {
    const response = await this.regenerateMessageRaw(
      { regenerateMessageV2Dto: regenerateMessageV2Dto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * Sends a message to an existing chat and returns a server-sent event stream with AI response chunks
   * Send a message to an existing chat
   */
  async sendMessageRaw(
    requestParameters: ChatV2ControllerSendMessageRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ChatV2ControllerCreateChat200Response>> {
    if (requestParameters.sendMessageDto == null) {
      throw new runtime.RequiredError(
        'sendMessageDto',
        'Required parameter "sendMessageDto" was null or undefined when calling sendMessage().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v2/chatAssistant/sendMessage`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: SendMessageDtoToJSON(requestParameters.sendMessageDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ChatV2ControllerCreateChat200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Sends a message to an existing chat and returns a server-sent event stream with AI response chunks
   * Send a message to an existing chat
   */
  async sendMessage(
    sendMessageDto: SendMessageDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ChatV2ControllerCreateChat200Response> {
    const response = await this.sendMessageRaw({ sendMessageDto: sendMessageDto }, initOverrides);
    return await response.value();
  }
}
