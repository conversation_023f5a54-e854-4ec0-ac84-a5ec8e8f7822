import type { Editor } from '@tiptap/react';
import { throttle } from 'lodash-es';
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { type PatchThought, type Thought, ThoughtTitleTypeEnum } from '@/typings/thought';
import {
  MobileEditor,
  type MobileEditorProps,
  type MobileEditorRef,
} from '../../editor-kit/thought-mobile-editor/mobile-editor';
import { ThoughtGenerateTitleIcon } from '../icon/thought-generate-title';
import type { ThoughtBodyComponentRef } from '../type';
import type { MobileThoughtWorkflow } from '../workflow/mobile-thought-workflow';
import './mobile-thought-body.css';

export type MobileThoughtBodyComponentProps = {
  id: string;
  thought: Thought | null;
  workflow: MobileThoughtWorkflow | null;
  mobileEditorProps: MobileEditorProps;
  onUpdate?: (data: PatchThought) => void;
};

const MobileThoughtBodyComponentBase = forwardRef<
  ThoughtBodyComponentRef,
  MobileThoughtBodyComponentProps
>(({ workflow, thought, mobileEditorProps, onUpdate }, ref) => {
  const mobileEditorRef = useRef<MobileEditorRef>(null);
  const [isGenTitle, setIsGenTitle] = useState(false);
  const [title, setTitle] = useState(thought?.title ?? '');
  const [editor, setEditor] = useState<Editor | null>(null);
  const [titleType, setTitleType] = useState<ThoughtTitleTypeEnum>(
    thought?.title_type ?? ThoughtTitleTypeEnum.default,
  );
  const titleElRef = useRef<HTMLDivElement>(null);
  const isInitializedRef = useRef(false);
  const isGenTitleRef = useRef(isGenTitle);
  const titleTypeRef = useRef(titleType);

  // 更新 ref 值以跟踪最新状态
  useEffect(() => {
    isGenTitleRef.current = isGenTitle;
  }, [isGenTitle]);

  useEffect(() => {
    titleTypeRef.current = titleType;
  }, [titleType]);

  // 初始化标题内容
  useEffect(() => {
    if (!titleElRef.current) return;

    const initialText = titleType === ThoughtTitleTypeEnum.default ? '' : title;
    if (titleElRef.current.textContent !== initialText) {
      titleElRef.current.textContent = initialText;
    }
    isInitializedRef.current = true;
  }, []); // 只在组件挂载时执行一次

  // 处理标题类型变化
  useEffect(() => {
    if (!titleElRef.current || !isInitializedRef.current) return;

    // 只在元素没有焦点时更新，避免干扰用户输入
    if (!titleElRef.current.matches(':focus')) {
      const shouldShowTitle = titleType !== ThoughtTitleTypeEnum.default;
      if (titleElRef.current.textContent !== (shouldShowTitle ? title : '')) {
        titleElRef.current.textContent = shouldShowTitle ? title : '';
      }
    }
  }, [titleType]); // 只在 titleType 改变时更新，不监听 title 变化

  // 创建节流的 editTitle 函数，300ms 执行一次
  const throttledEditTitle = useMemo(
    () =>
      throttle((text: string) => {
        workflow?.editTitle(text);
      }, 300),
    [workflow],
  );

  // 组件卸载时取消节流
  useEffect(() => {
    return () => {
      throttledEditTitle.cancel();
    };
  }, [throttledEditTitle]);

  useImperativeHandle(ref, () => ({
    getTitleEl: () => titleElRef.current,
    getIsGenTitle: () => isGenTitleRef.current,
    getTitleType: () => titleTypeRef.current,
    getTitle: () => titleElRef.current?.textContent || '',
    setTitleType: (titleType: ThoughtTitleTypeEnum) => setTitleType(titleType),
    setIsGenTitle: (isGenTitle: boolean) => setIsGenTitle(isGenTitle),
    setTitle: (newTitle: string) => {
      setTitle(newTitle);
      if (titleElRef.current && !titleElRef.current.matches(':focus')) {
        // 只在元素没有焦点时更新DOM，避免干扰用户输入
        const shouldShowTitle = titleTypeRef.current !== ThoughtTitleTypeEnum.default;
        titleElRef.current.textContent = shouldShowTitle ? newTitle : '';
      }
    },
    onUpdate: (data: PatchThought) => {
      onUpdate?.(data);
    },
  }));

  const onEditTitle = useCallback(() => {
    if (!titleElRef.current) return;
    const newTitle = titleElRef.current.textContent || '';
    if (newTitle.length <= 255) {
      // 在失去焦点时立即更新，确保数据同步
      throttledEditTitle.cancel(); // 取消之前的节流
      workflow?.editTitle(newTitle);
    }
  }, [workflow, throttledEditTitle]);

  const handleInput = useCallback(() => {
    if (!titleElRef.current) return;
    const text = titleElRef.current.textContent || '';

    // 如果文本为空，确保元素真正为空以显示placeholder
    if (!text || !text.trim()) {
      // 清空所有内容，包括 br 标签
      while (titleElRef.current.firstChild) {
        titleElRef.current.removeChild(titleElRef.current.firstChild);
      }
    }

    // 更新本地状态
    if (text.length <= 255) {
      setTitle(text);
      throttledEditTitle(text);
    } else {
      // 超过255字符时截断
      const truncatedText = text.slice(0, 255);
      titleElRef.current.textContent = truncatedText;
      setTitle(truncatedText);
      throttledEditTitle(truncatedText);

      // 将光标移到末尾
      const range = document.createRange();
      const sel = window.getSelection();
      range.selectNodeContents(titleElRef.current);
      range.collapse(false);
      sel?.removeAllRanges();
      sel?.addRange(range);
    }
  }, [workflow]);

  const handleGenerateTitle = useCallback(() => {
    if (isGenTitle || !workflow) {
      return;
    }
    workflow.manualUpdateAITitle();
  }, [workflow, isGenTitle]);

  return (
    <div className="flex flex-col w-full h-auto bg-transparent mobile-thought-body-container">
      <div className="flex items-start justify-between w-full mb-6">
        <div
          className="min-h-[36px] border-none bg-transparent px-0 py-0 text-2xl font-[600] outline-none empty:before:content-[attr(data-placeholder)] empty:before:text-disabled-foreground"
          contentEditable
          spellCheck={false}
          ref={titleElRef}
          data-placeholder={isGenTitle ? 'Generating...' : 'New thought'}
          onInput={handleInput}
          onBlur={onEditTitle}
          enterKeyHint="next"
          suppressContentEditableWarning
          onKeyDown={(e) => {
            // 如果正在使用输入法，不执行操作
            if (e.nativeEvent.isComposing || e.keyCode === 229) {
              return;
            }
            if (e.key === 'Enter') {
              e.preventDefault();
              editor?.commands.focus('start');
            }
            // 禁止在输入框中按 command + s 保存
            if (e.key === 's' && (e.metaKey || e.ctrlKey)) {
              e.preventDefault();
            }
            // 注意：移动端不在 keydown 时更新数据，只在 input 事件时更新
          }}
        />
        <div className="flex p-2 pt-1" onClick={handleGenerateTitle}>
          <ThoughtGenerateTitleIcon size={20} />
        </div>
      </div>
      <MobileEditor
        {...mobileEditorProps}
        ref={mobileEditorRef}
        content={thought?.content.raw}
        onCreate={(params) => {
          setEditor(params.editor);
          mobileEditorProps.onCreate?.(params);
        }}
      />
    </div>
  );
});

MobileThoughtBodyComponentBase.displayName = 'MobileThoughtBodyComponentBase';

export const MobileThoughtBodyComponent = memo(
  MobileThoughtBodyComponentBase,
  (prevProps, nextProps) => {
    return prevProps.id === nextProps.id;
  },
);

MobileThoughtBodyComponent.displayName = 'MobileThoughtBodyComponent';
