import type { UserWithPreferenceSpaceDto } from '@repo/api/generated-client/snake-case/index';
import { toast } from '@repo/ui/components/ui/sonner';
import type { RefObject } from 'react';
import type { IndexeddbPersistence } from 'y-indexeddb';
import { type PatchThought, ThoughtTitleTypeEnum } from '@/typings/thought';
import type { AIOptions, ThoughtBodyComponentRef } from '../type';
import { BaseThoughtWorkflow, type BaseThoughtWorkflowOptions } from './base-thought-workflow';

export interface ThoughtWorkflowOptions extends Omit<BaseThoughtWorkflowOptions, 'componentRef'> {
  indexedDB: IndexeddbPersistence | null;
  thoughtComponentRef: RefObject<ThoughtBodyComponentRef>;
  aiOptionsRef?: RefObject<AIOptions>;
  user: UserWithPreferenceSpaceDto;
}

export class ThoughtWorkflow extends BaseThoughtWorkflow {
  aiOptionsRef?: RefObject<AIOptions>;

  user: UserWithPreferenceSpaceDto;

  constructor(options: ThoughtWorkflowOptions) {
    super({
      id: options.id,
      editor: options.editor,
      ydoc: options.ydoc,
      indexedDB: options.indexedDB,
      componentRef: options.thoughtComponentRef,
    });
    this.aiOptionsRef = options.aiOptionsRef;
    this.editor.on('selectionUpdate', this.handleSelectionChange);
    this.user = options.user;
  }

  protected performUpdate(data: PatchThought): void {
    this.thoughtComponent?.onUpdate?.(data);
  }

  protected handleUpdateAITitleFailed(description: string): void {
    toast(description);
  }

  // 重写 editTitle 方法以包含 dongdong-thought 特有的逻辑
  editTitle(title: string) {
    // 过滤回车换行符
    const filteredTitle = title.replace(/[\r\n]/g, '');

    // dongdong-thought 特有：先调用 onUpdate
    this.thoughtComponent?.onUpdate?.({
      title: filteredTitle,
      title_type: ThoughtTitleTypeEnum.manual,
    });

    this.thoughtComponent?.setTitleType(ThoughtTitleTypeEnum.manual);
    this.setTitle(filteredTitle);
    this.thoughtTitleUtils.broadcastTitleChange(filteredTitle);
    this.onEditorUpdate();
  }

  destroy() {
    this.editor.off('selectionUpdate', this.handleSelectionChange);
    super.destroy();
  }

  protected handleFailed(description: string): void {
    toast(description);
  }

  get aiOptions() {
    return this.aiOptionsRef?.current;
  }

  get userInfo() {
    return this.user;
  }
}

export type IThoughtWorkflow = ThoughtWorkflow;
