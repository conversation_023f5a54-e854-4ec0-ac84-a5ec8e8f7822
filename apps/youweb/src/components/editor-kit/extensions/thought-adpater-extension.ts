import {
  addSelectionNodes,
  base64To<PERSON><PERSON>N,
  DIFF_CHANGE_TYPE,
  diffTransformUtils,
  markdownParse,
  markdownSerializer,
  removeAllSelectionNodes,
} from '@repo/editor-common';
import { getDiffBlockManage } from '@repo/ui-business-editor';
import { Extension } from '@tiptap/core';
import type { Editor } from '@tiptap/react';
import type { RefObject } from 'react';
import { Thought } from '@/typings/thought';
import type { ThoughtBodyComponentRef } from '../../thought/type';
import type { IThoughtWorkflow } from '../../thought/workflow';
import { ThoughtLocalInfo } from './thought-local-info';

export const THOUGHT_ADAPTER_EXTENSION_NAME = 'thoughtAdapter';

export const getWorkflow = (editor: Editor): IThoughtWorkflow | null => {
  return editor.storage[THOUGHT_ADAPTER_EXTENSION_NAME].workflow;
};

export const setWorkflow = (editor: Editor, workflow: IThoughtWorkflow): void => {
  editor.storage[THOUGHT_ADAPTER_EXTENSION_NAME].workflow = workflow;
};

export const getThoughtBodyComponentRef = (editor: Editor): ThoughtBodyComponentRef | null => {
  return editor.storage[THOUGHT_ADAPTER_EXTENSION_NAME].thoughtBodyComponentRef;
};

export const setThoughtBodyComponentRef = (
  editor: Editor,
  ref: RefObject<ThoughtBodyComponentRef>,
): void => {
  editor.storage[THOUGHT_ADAPTER_EXTENSION_NAME].thoughtBodyComponentRef = ref;
};

// 为 Tiptap 扩展命令声明类型
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    thoughtAdapter: {
      insertContentByMarkdown: (markdown: string) => ReturnType;
      getEditorAIWriterMarkdownContent: () => ReturnType;
      clearViewSelection: () => ReturnType;
      setAIWriterContent: (data: Thought) => ReturnType;
    };
  }
}

export interface ThoughtAdapterStorage {
  workflow: IThoughtWorkflow | null;
  thoughtBodyComponentRef: RefObject<ThoughtBodyComponentRef> | null;
  localInfo: ThoughtLocalInfo | null;
}

export interface ThoughtAdapterOptions {
  id: string;
  localInfoEnable: boolean;
}

export const ThoughtAdapter = Extension.create<ThoughtAdapterOptions, ThoughtAdapterStorage>({
  name: THOUGHT_ADAPTER_EXTENSION_NAME,

  addOptions() {
    return {
      id: '',
      localInfoEnable: true,
    };
  },

  addStorage() {
    return {
      workflow: null,
      thoughtBodyComponentRef: null,
      localInfo: null,
    } as ThoughtAdapterStorage;
  },

  onCreate() {
    if (this.options.localInfoEnable) {
      this.storage.localInfo = new ThoughtLocalInfo({
        id: this.options.id,
        editor: this.editor,
      });
      setTimeout(() => {
        this.storage.localInfo?.setupEventListeners();
        this.storage.localInfo?.tryRecoverPosition();
      }, 50);
    }
  },

  onDestroy() {
    if (this.storage.localInfo) {
      this.storage.localInfo.destroy();
      this.storage.localInfo = null;
    }
  },

  addCommands() {
    return {
      insertContentByMarkdown: (markdown: string) => () => {
        const editorHTMLContent = markdownParse.parse(markdown);

        // 在当前光标位置插入内容
        const { selection } = this.editor.state;
        const insertPos = selection.to;
        const oldDocLength = this.editor.state.doc.content.size;

        // 使用 insertContent 插入内容
        try {
          this.editor.commands.insertContent(editorHTMLContent);
        } catch (error) {
          // do nothing
        }

        // 插入成功后，根据内容变化调整光标位置
        // 延迟执行，确保DOM更新完成
        setTimeout(() => {
          const newDoc = this.editor.state.doc;
          const newDocLength = newDoc.content.size;

          let newPos: number;

          if (newDocLength >= oldDocLength) {
            // 内容变长或不变，将光标移动到插入内容的末尾
            const insertedLength = newDocLength - oldDocLength;
            newPos = Math.min(insertPos + insertedLength, newDoc.content.size);
          } else {
            // 内容变短，光标位置保持不变（但确保不超出文档范围）
            newPos = Math.min(insertPos, newDoc.content.size);
          }

          this.editor.commands.setTextSelection(newPos);

          // 滚动到光标位置，确保光标在可见范围内
          this.editor.commands.scrollIntoView();
        }, 500);
        return true;
      },
      getEditorAIWriterMarkdownContent: () => () => {
        addSelectionNodes(this.editor);
        const docWithoutDiffInfo = diffTransformUtils.extractContent(
          this.editor.state.doc,
          DIFF_CHANGE_TYPE.ADDED,
        );
        const content = markdownSerializer.serialize(docWithoutDiffInfo);
        removeAllSelectionNodes(this.editor);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return content as any;
      },
      setAIWriterContent: (data: Thought) => () => {
        const { content } = data;
        const workflow = getWorkflow(this.editor);
        if (!workflow) {
          return false;
        }
        const { title } = data;
        if (title) {
          workflow.editTitle(title);
        }
        if (content) {
          const jsonContent = base64ToJSON(content.raw || '');
          this.editor.commands.setContent(jsonContent);
          this.editor.emit('update', { editor: this.editor, transaction: this.editor.state.tr });
          const diffBlockManage = getDiffBlockManage(this.editor);
          diffBlockManage?.reRenderDiffInfo();
        }
        return true;
      },
      clearViewSelection: () => () => {
        // 获取当前选区位置
        const { from } = this.editor.state.selection;

        // 如果当前有选中内容，将光标移动到选区开始位置
        if (!this.editor.state.selection.empty) {
          this.editor.commands.setTextSelection(from);
        }

        return true;
      },
    };
  },

  addKeyboardShortcuts() {
    return {
      Backspace: () => {
        const editor = this.editor;
        const { selection } = editor.state;
        const { $from, $to } = selection;

        const hasSelection = $from.pos !== $to.pos;

        if (hasSelection) {
          return false;
        }

        const isAtStart = $from.pos === 1;
        const currentNode = $from.parent;
        const isEmpty = currentNode.textContent === '' && $from.parentOffset === 0;

        if (!isAtStart) return false;

        if (isEmpty) {
          editor?.commands.deleteRange({
            from: $from.before(),
            to: $from.pos,
          });
          editor?.commands.focus('start');
        }

        return true;
      },
    };
  },
});
