import { CreateThoughtVersion, PatchThought } from '@/typings/thought';
import { apiClient, callAPI } from '@/utils/callHTTP';

export const patchThought = async (params: PatchThought) => {
  return callAPI(apiClient.thoughtApi.patchThought(params), {
    silent: true,
  });
};

export const createThoughtVersion = async (params: CreateThoughtVersion) => {
  return callAPI(apiClient.thoughtVersionApi.createThoughtVersion(params), {
    silent: true,
  });
};

export const deleteThoughtVersion = async (id: string) => {
  return callAPI(apiClient.thoughtVersionApi.deleteThoughtVersion({ id }), {
    silent: true,
  });
};

export const listThoughtVersions = async (thoughtId: string) => {
  return callAPI(apiClient.thoughtVersionApi.listThoughtVersions({ thought_id: thoughtId }), {
    silent: true,
  });
};

export const getThought = async (thoughtId: string) => {
  return callAPI(apiClient.thoughtApi.getThought({ id: thoughtId }), {
    silent: true,
  });
};
