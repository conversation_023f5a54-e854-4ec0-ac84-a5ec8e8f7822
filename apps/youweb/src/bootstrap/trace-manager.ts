import { Context, context, Span, SpanKind, SpanStatusCode, trace } from '@opentelemetry/api';
import { WebTracerProvider } from '@opentelemetry/sdk-trace-web';
import { type RootSpanType, SpanEndReason, SpanType } from './trace-types';

interface ActiveRootSpan {
  span: Span;
  type: RootSpanType;
  startTime: number;
  requestCount: number;
  timer?: NodeJS.Timeout;
  ended: boolean;
}

/**
 * 集中管理根 span 的生命周期，确保同一类型只有一个活跃 span，避免孤儿 span
 */
export class TraceManager {
  private activeRootSpans = new Map<RootSpanType, ActiveRootSpan>();
  private provider: WebTracerProvider;
  private sessionAttributes: Record<string, any>;
  private readonly MAX_WINDOW_MS = 10000; // 10 seconds max window for safety

  constructor(provider: WebTracerProvider, sessionAttributes: Record<string, any>) {
    this.provider = provider;
    this.sessionAttributes = sessionAttributes;
  }

  /**
   * 启动指定类型的根 span，会自动结束同类型的现有 span
   */
  startRootSpan(
    type: RootSpanType,
    name: string,
    attributes: Record<string, any> = {},
    parentContext?: Context,
    handoffFrom?: RootSpanType,
  ): Span {
    this.endRootSpan(type, SpanEndReason.REPLACED_BY_NEW);

    // 处理从其他 span 类型的交接，比如用户点击导致的导航
    if (handoffFrom) {
      this.endRootSpan(handoffFrom, SpanEndReason.HANDOFF);
    }

    const tracer = trace.getTracer('youweb');

    // 只有页面加载需要继承中间件的 trace context，其他类型都是独立的根 span
    const spanContext = type === SpanType.PAGE_LOAD && parentContext ? parentContext : undefined;

    const span = tracer.startSpan(
      name,
      {
        kind: SpanKind.INTERNAL,
        attributes: {
          'span.type': type,
          ...this.sessionAttributes,
          ...attributes,
        },
      },
      spanContext,
    );

    const activeSpan: ActiveRootSpan = {
      span,
      type,
      startTime: Date.now(),
      requestCount: 0,
      ended: false,
    };

    this.activeRootSpans.set(type, activeSpan);

    // Set max timeout for non-background spans
    if (type !== SpanType.BACKGROUND_ACTIVITY) {
      activeSpan.timer = setTimeout(() => {
        this.endRootSpan(type, SpanEndReason.MAX_TIMEOUT);
      }, this.MAX_WINDOW_MS);
    }

    return span;
  }

  /**
   * 结束根 span 并强制导出，确保父 span 在子 span 之前到达 New Relic
   */
  endRootSpan(type: RootSpanType, reason: SpanEndReason): void {
    const activeSpan = this.activeRootSpans.get(type);
    if (!activeSpan || activeSpan.ended) return;

    if (activeSpan.timer) {
      clearTimeout(activeSpan.timer);
    }

    activeSpan.span.setAttribute('span.end_reason', reason);
    activeSpan.span.setAttribute('span.duration_ms', Date.now() - activeSpan.startTime);
    activeSpan.span.setAttribute('span.request_count', activeSpan.requestCount);
    activeSpan.span.setStatus({ code: SpanStatusCode.OK });
    activeSpan.span.end();
    activeSpan.ended = true;

    // 立即导出父 span，防止子 span 先到达造成孤儿 span
    this.provider.forceFlush().catch((err) => {
      console.warn(`Failed to flush ${type} span:`, err);
    });

    this.activeRootSpans.delete(type);
  }

  /**
   * 为 HTTP 请求选择合适的父上下文，优先级：导航 > 交互 > 页面加载 > 背景活动
   * 注意：导航优先级高于交互，因为用户点击菜单触发的导航，其HTTP请求应归属导航而非点击
   */
  getHttpParentContext(): { context: Context; spanType: RootSpanType } | null {
    // 调整优先级：导航 > 交互，确保导航相关HTTP请求正确归属
    const priorities: RootSpanType[] = [
      SpanType.NAVIGATION,
      SpanType.USER_INTERACTION,
      SpanType.PAGE_LOAD,
      SpanType.BACKGROUND_ACTIVITY,
    ];

    for (const type of priorities) {
      const activeSpan = this.activeRootSpans.get(type);
      if (activeSpan && !activeSpan.ended) {
        // Simplified - since spans end quickly, we don't need complex age checking
        return {
          context: trace.setSpan(context.active(), activeSpan.span),
          spanType: type,
        };
      }
    }

    // 没有活跃的根 span 时创建背景活动 span，确保 HTTP 请求不会成为孤儿
    const span = this.startRootSpan(SpanType.BACKGROUND_ACTIVITY, 'background-activity', {
      'background.reason': 'no_active_root_span',
    });

    return {
      context: trace.setSpan(context.active(), span),
      spanType: SpanType.BACKGROUND_ACTIVITY,
    };
  }

  isRootSpanActive(type: RootSpanType): boolean {
    const activeSpan = this.activeRootSpans.get(type);
    return !!(activeSpan && !activeSpan.ended);
  }

  getActiveRootSpan(type: RootSpanType): Span | null {
    const activeSpan = this.activeRootSpans.get(type);
    return activeSpan && !activeSpan.ended ? activeSpan.span : null;
  }

  cleanup(): void {
    for (const [type, activeSpan] of Array.from(this.activeRootSpans.entries())) {
      if (!activeSpan.ended) {
        this.endRootSpan(type, SpanEndReason.WINDOW_UNLOAD);
      }
    }
  }
}
