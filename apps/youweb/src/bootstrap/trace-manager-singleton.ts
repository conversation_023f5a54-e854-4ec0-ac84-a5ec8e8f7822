import type { WebTracerProvider } from '@opentelemetry/sdk-trace-web';
import { TraceManager } from './trace-manager';

let traceManager: TraceManager | null = null;

export function initializeTraceManager(
  provider: WebTracerProvider,
  sessionAttributes: Record<string, any>,
): TraceManager {
  if (traceManager) {
    console.warn('[TraceManager] Already initialized, returning existing instance');
    return traceManager;
  }
  traceManager = new TraceManager(provider, sessionAttributes);
  return traceManager;
}

export function getTraceManager(): TraceManager {
  if (!traceManager) {
    throw new Error('TraceManager not initialized. Ensure otel.ts has been imported.');
  }
  return traceManager;
}

export function isTraceManagerInitialized(): boolean {
  return traceManager !== null;
}
