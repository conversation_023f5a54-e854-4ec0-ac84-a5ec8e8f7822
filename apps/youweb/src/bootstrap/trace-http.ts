import { context, propagation, SpanKind, SpanStatusCode, trace } from '@opentelemetry/api';
import { getTraceManager, isTraceManagerInitialized } from './trace-manager-singleton';

const tracer = trace.getTracer('youweb-http');

// 避免循环导入 JSX 文件，通过全局变量获取路由上下文
const getRouteContext = async () => {
  return (globalThis as any).__ROUTE_CONTEXT__ || null;
};

export function instrumentFetch() {
  const originalFetch = window.fetch;

  if ((originalFetch as any).__instrumented) {
    return;
  }

  window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    // 防止对自己发送的 telemetry 请求进行 trace，避免无限递归
    if ((init as any)?.__skipTracing) {
      return originalFetch(input, init);
    }

    const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
    const method = init?.method || 'GET';

    if (!isTraceManagerInitialized()) {
      console.error('[HTTP] TraceManager not initialized, skipping tracing');
      return originalFetch(input, init);
    }

    const traceManager = getTraceManager();
    const parentInfo = traceManager.getHttpParentContext();
    if (!parentInfo) {
      throw new Error('Failed to get parent context for HTTP request');
    }

    const { context: parentContext } = parentInfo;
    // 在正确的父上下文中创建 HTTP span
    const span = tracer.startSpan(
      `${method} ${new URL(url, window.location.origin).pathname}`,
      {
        kind: SpanKind.CLIENT,
        attributes: {
          'http.method': method,
          'http.url': url,
          'http.target': new URL(url, window.location.origin).pathname,
        },
      },
      parentContext,
    );

    // 添加路由上下文
    const routeContext = await getRouteContext();

    if (routeContext && Date.now() - routeContext.timestamp < 10 * 60 * 1000) {
      span.setAttributes({
        'route.pathname': routeContext.pathname,
        'route.navigation_type': routeContext.navigationType,
        'route.time_since_navigation_ms': Date.now() - routeContext.timestamp,
      });
    }

    // 注入追踪头到 HTTP 请求
    const headers = new Headers(init?.headers);
    const contextWithSpan = trace.setSpan(parentContext, span);
    propagation.inject(contextWithSpan, headers, {
      set: (carrier, key, value) => {
        (carrier as Headers).set(key, value);
      },
    });

    try {
      // 在包含 span 的上下文中执行请求，添加跳过标记防止递归
      const response = await context.with(trace.setSpan(parentContext, span), async () => {
        return originalFetch(input, { ...init, headers, __skipTracing: true } as RequestInit);
      });

      span.setAttributes({
        'http.status_code': response.status,
        'http.response_content_length': response.headers.get('content-length') || 0,
      });

      if (response.status >= 400) {
        span.setStatus({ code: SpanStatusCode.ERROR, message: `HTTP ${response.status}` });
      } else {
        span.setStatus({ code: SpanStatusCode.OK });
      }

      return response;
    } catch (error) {
      // Distinguish between different error types
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const isAborted = error instanceof Error && error.name === 'AbortError';
      const isTimeout = errorMessage.toLowerCase().includes('timeout');
      const isNetworkError = error instanceof TypeError && errorMessage.includes('Failed to fetch');

      span.recordException(error as Error);

      if (isAborted) {
        span.setStatus({
          code: SpanStatusCode.OK,
          message: 'Request cancelled',
        });
        span.setAttribute('http.cancelled', true);
        span.setAttribute('http.cancel_reason', 'user_abort');
      } else if (isTimeout) {
        // Request timed out
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: 'Request timeout',
        });
        span.setAttribute('http.timeout', true);
        span.setAttribute('error.type', 'timeout');
      } else if (isNetworkError) {
        // Network failure
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: 'Network error',
        });
        span.setAttribute('error.type', 'network');
      } else {
        // Other errors
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: errorMessage,
        });
        span.setAttribute('error.type', 'unknown');
      }

      throw error;
    } finally {
      span.end();
    }
  };

  // 标记已经 instrumented
  (window.fetch as any).__instrumented = true;
}
