import type { TraceManager } from './trace-manager';
import { getTraceManager, isTraceManagerInitialized } from './trace-manager-singleton';
import { SpanEndReason, SpanType } from './trace-types';

// 避免循环导入 JSX 文件，通过全局变量获取路由上下文
const getRouteContext = () => {
  return (globalThis as any).__ROUTE_CONTEXT__ || null;
};

let traceManager: TraceManager | null = null;
let hasActiveInteraction = false;

function getElementInfo(element: Element) {
  const tag = element.tagName.toLowerCase();
  const id = element.id;
  const text = element.textContent?.trim().slice(0, 20) || '';
  const role = element.getAttribute('role');
  const ariaLabel = element.getAttribute('aria-label');

  let name = tag;
  if (id) {
    name += `#${id}`;
  }
  if (role) {
    name += `[${role}]`;
  }
  if (ariaLabel) {
    name += `:"${ariaLabel.slice(0, 15)}"`;
  } else if (text) {
    name += `:"${text.slice(0, 15)}"`;
  }

  return { name, tag, id, text, role, ariaLabel };
}

function shouldIgnoreElement(element: Element) {
  const tag = element.tagName.toLowerCase();
  const ignoredTags = ['html', 'body'];

  if (ignoredTags.includes(tag)) {
    return true;
  }

  const isInteractive = ['button', 'a', 'input', 'select', 'textarea'].includes(tag);
  if (isInteractive) {
    return false;
  }

  const hasId = !!element.id;
  const hasRole = !!element.getAttribute('role');
  const hasAriaLabel = !!element.getAttribute('aria-label');
  const hasClickHandler =
    element.getAttribute('onclick') || (element as HTMLElement).style?.cursor === 'pointer';

  if (hasId || hasRole || hasAriaLabel || hasClickHandler) {
    return false;
  }

  return true;
}

async function startPendingInteraction(eventType: string, element: Element) {
  if (shouldIgnoreElement(element)) {
    return;
  }

  if (!traceManager) {
    if (!isTraceManagerInitialized()) {
      console.warn('[Interaction] TraceManager not initialized, skipping interaction tracking');
      return;
    }
    traceManager = getTraceManager();
  }

  if (hasActiveInteraction) {
    traceManager.endRootSpan(SpanType.USER_INTERACTION, SpanEndReason.NEW_INTERACTION);
  }

  const elementInfo = getElementInfo(element);
  const routeContext = getRouteContext();

  const interactionAttributes: Record<string, any> = {
    'interaction.type': eventType,
    'element.tag': elementInfo.tag,
    'element.id': elementInfo.id || '',
    'element.text': elementInfo.text,
    'element.role': elementInfo.role || '',
    'element.aria_label': elementInfo.ariaLabel || '',
  };

  // 只记录 10 分钟内的路由上下文，过旧的信息无意义
  if (routeContext && Date.now() - routeContext.timestamp < 10 * 60 * 1000) {
    interactionAttributes['route.pathname'] = routeContext.pathname;
    interactionAttributes['route.navigation_type'] = routeContext.navigationType;
    interactionAttributes['route.time_since_navigation_ms'] = Date.now() - routeContext.timestamp;
  }

  traceManager.startRootSpan(
    SpanType.USER_INTERACTION,
    `${eventType}-${elementInfo.name}`,
    interactionAttributes,
  );

  hasActiveInteraction = true;

  setTimeout(() => {
    if (traceManager && hasActiveInteraction) {
      traceManager.endRootSpan(SpanType.USER_INTERACTION, SpanEndReason.NO_HTTP_ACTIVITY);
      hasActiveInteraction = false;
    }
  }, 200);
}

export function instrumentInteractions() {
  document.addEventListener(
    'click',
    (event) => {
      if (event.target instanceof Element) {
        startPendingInteraction('click', event.target);
      }
    },
    { capture: true, passive: true },
  );

  document.addEventListener(
    'keyup',
    (event) => {
      const meaningfulKeys = ['Enter', 'Space', 'Escape', 'Tab'];
      if (meaningfulKeys.includes(event.code) && event.target instanceof Element) {
        startPendingInteraction('keyup', event.target);
      }
    },
    { capture: true, passive: true },
  );
}
