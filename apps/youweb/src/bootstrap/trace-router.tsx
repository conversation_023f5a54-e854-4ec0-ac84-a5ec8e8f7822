import { useEffect, useLayoutEffect, useRef } from 'react';
import { useLocation, useNavigationType } from 'react-router';
import type { TraceManager } from './trace-manager';
import { getTraceManager, isTraceManagerInitialized } from './trace-manager-singleton';
import { SpanEndReason, SpanType } from './trace-types';

let currentRouteContext: {
  pathname: string;
  navigationType: string;
  timestamp: number;
} | null = null;

export const getCurrentRouteContext = () => currentRouteContext;

export function useRouteTracing() {
  const location = useLocation();
  const navigationType = useNavigationType();
  const previousPathRef = useRef<string>('');
  const traceManagerRef = useRef<TraceManager | null>(null);

  useLayoutEffect(() => {
    const init = async () => {
      if (previousPathRef.current === location.pathname) {
        return;
      }

      if (traceManagerRef.current) {
        traceManagerRef.current.endRootSpan(SpanType.NAVIGATION, SpanEndReason.NEW_NAVIGATION);
      }

      previousPathRef.current = location.pathname;

      if (!isTraceManagerInitialized()) {
        console.warn('[Router] TraceManager not initialized, skipping route tracing');
        return;
      }
      const traceManager = getTraceManager();
      traceManagerRef.current = traceManager;

      traceManager.startRootSpan(
        SpanType.NAVIGATION,
        `React Router Navigation ${location.pathname}`,
        {
          'navigation.type': navigationType,
          'navigation.pathname': location.pathname,
          'navigation.search': location.search,
          'navigation.hash': location.hash,
          'navigation.from': previousPathRef.current || 'initial',
        },
      );

      setTimeout(() => {
        traceManager.endRootSpan(SpanType.NAVIGATION, SpanEndReason.NEW_NAVIGATION);
      }, 100);

      currentRouteContext = {
        pathname: location.pathname,
        navigationType,
        timestamp: Date.now(),
      };
      (globalThis as any).__ROUTE_CONTEXT__ = currentRouteContext;
    };
    init();
  }, [location, navigationType]);

  useEffect(() => {
    return () => {
      if (traceManagerRef.current) {
        traceManagerRef.current.endRootSpan(SpanType.NAVIGATION, SpanEndReason.COMPONENT_UNMOUNT);
      }
    };
  }, []);
}
