import { Context, SpanKind } from '@opentelemetry/api';
import { ReadableSpan, Span, SpanProcessor } from '@opentelemetry/sdk-trace-base';

interface TraceInfo {
  spans: ReadableSpan[];
  timer: NodeJS.Timeout;
  maxTimer: NodeJS.Timeout;
  hasHttpSpan: boolean;
  startTime: number;
}

/**
 * 简单的追踪级过滤器：只保留包含 HTTP 请求的完整追踪
 */
export class SimpleTraceFilterSpanProcessor implements SpanProcessor {
  private traces = new Map<string, TraceInfo>();
  private readonly traceWaitTime = 3000; // 滚动窗口：3 秒空闲后处理追踪
  private readonly maxWaitTime = 10000; // 最大等待时间：10 秒后强制处理
  private readonly baseProcessor: SpanProcessor;

  constructor(baseProcessor: SpanProcessor) {
    this.baseProcessor = baseProcessor;
  }

  onStart(span: Span, parentContext: Context): void {
    this.baseProcessor.onStart(span, parentContext);
  }

  onEnd(span: ReadableSpan): void {
    const traceId = span.spanContext().traceId;
    const isHttpSpan = span.kind === SpanKind.CLIENT;

    let traceInfo = this.traces.get(traceId);

    if (!traceInfo) {
      const now = Date.now();
      traceInfo = {
        spans: [],
        timer: setTimeout(() => this.processTrace(traceId), this.traceWaitTime),
        maxTimer: setTimeout(() => this.processTrace(traceId), this.maxWaitTime),
        hasHttpSpan: false,
        startTime: now,
      };
      this.traces.set(traceId, traceInfo);
    }

    traceInfo.spans.push(span);

    if (isHttpSpan) {
      traceInfo.hasHttpSpan = true;

      // HTTP span 到达时重置滚动窗口定时器，但不重置最大定时器
      clearTimeout(traceInfo.timer);
      traceInfo.timer = setTimeout(() => this.processTrace(traceId), this.traceWaitTime);
    }
  }

  private processTrace(traceId: string): void {
    const traceInfo = this.traces.get(traceId);
    if (!traceInfo) {
      return;
    }

    // 清理定时器
    clearTimeout(traceInfo.timer);
    clearTimeout(traceInfo.maxTimer);

    // 如果追踪包含 HTTP span，发送所有 span；否则丢弃
    if (traceInfo.hasHttpSpan) {
      for (const span of traceInfo.spans) {
        this.baseProcessor.onEnd(span);
      }
    }

    // 清理
    this.traces.delete(traceId);
  }

  forceFlush(): Promise<void> {
    // 立即处理所有待处理的追踪
    for (const traceId of Array.from(this.traces.keys())) {
      this.processTrace(traceId);
    }
    return this.baseProcessor.forceFlush();
  }

  shutdown(): Promise<void> {
    // 清理所有定时器并处理剩余追踪
    for (const traceId of Array.from(this.traces.keys())) {
      this.processTrace(traceId);
    }
    return this.baseProcessor.shutdown();
  }
}
