import { context, propagation, trace } from '@opentelemetry/api';
import {
  CompositePropagator,
  W3CBaggagePropagator,
  W3CTraceContextPropagator,
} from '@opentelemetry/core';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { defaultResource, resourceFromAttributes } from '@opentelemetry/resources';
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-base';
import { WebTracerProvider } from '@opentelemetry/sdk-trace-web';
import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';
import { SimpleTraceFilterSpanProcessor } from './simple-trace-filter';
import { getTraceManager, initializeTraceManager } from './trace-manager-singleton';
import { SpanEndReason, SpanType } from './trace-types';

const resource = defaultResource().merge(
  resourceFromAttributes({
    [ATTR_SERVICE_NAME]: 'youweb',
    'service.namespace': 'youmind',
    'deployment.environment.name': process.env.YOUMIND_ENV || 'local',
  }),
);

const baseExporter = new OTLPTraceExporter({
  url: `${window.location.origin}/telemetry`,
});

// 创建简单的追踪级过滤器
const batchProcessor = new BatchSpanProcessor(baseExporter);
const filterProcessor = new SimpleTraceFilterSpanProcessor(batchProcessor);

const provider = new WebTracerProvider({
  resource,
  spanProcessors: [filterProcessor],
});

provider.register({
  propagator: new CompositePropagator({
    propagators: [new W3CTraceContextPropagator(), new W3CBaggagePropagator()],
  }),
});

import { instrumentFetch } from './trace-http';
import { instrumentInteractions } from './trace-interaction';
import { setupSentryIntegration } from './trace-sentry';

instrumentFetch();
setupSentryIntegration();
instrumentInteractions();

const sessionId = crypto.randomUUID();
const sessionStartTime = Date.now();

const sessionAttributes = {
  'session.id': sessionId,
  'session.start_url': window.location.href,
  'session.referrer': document.referrer,
  'session.user_agent': navigator.userAgent,
  'session.start_time': sessionStartTime,
};

const traceManager = initializeTraceManager(provider, sessionAttributes);

let parentContext = context.active();
const injectedTraceparent = (window as any).__TRACE_PARENT__;
if (injectedTraceparent) {
  const headers = new Map([['traceparent', injectedTraceparent]]);
  parentContext = propagation.extract(context.active(), headers, {
    get: (carrier, key) => {
      const value = (carrier as Map<string, string>).get(key);
      return value !== undefined ? value : undefined;
    },
    keys: (carrier) => Array.from((carrier as Map<string, string>).keys()),
  });
}

const pageLoadSpan = traceManager.startRootSpan(
  SpanType.PAGE_LOAD,
  `Initial Page Load ${window.location.href.replace(window.location.origin, '')}`,
  {
    'page.url': window.location.href,
    'page.referrer': document.referrer,
  },
  parentContext,
);

if (document.readyState === 'complete') {
  setTimeout(() => {
    pageLoadSpan.setAttribute('page.load_time', performance.now());
    pageLoadSpan.setAttribute('page.ready_state', document.readyState);
    traceManager.endRootSpan(SpanType.PAGE_LOAD, SpanEndReason.PAGE_READY);
  }, 100);
} else {
  window.addEventListener('load', () => {
    pageLoadSpan.setAttribute('page.load_time', performance.now());
    pageLoadSpan.setAttribute('page.ready_state', document.readyState);
    traceManager.endRootSpan(SpanType.PAGE_LOAD, SpanEndReason.PAGE_READY);
  });
}

window.addEventListener('beforeunload', () => {
  traceManager.cleanup();
});

export const getSessionAttributes = () => sessionAttributes;
export const tracer = trace.getTracer('youweb');
export { getTraceManager };
