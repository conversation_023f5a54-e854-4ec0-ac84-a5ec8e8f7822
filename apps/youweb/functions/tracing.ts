/**
 * 极简 OTEL 追踪模块
 * 为 Cloudflare Pages Functions 提供分布式追踪支持
 */

interface Env {
  NEW_RELIC_LICENSE_KEY?: string;
  ENVIRONMENT?: string;
}

interface TraceContext {
  version: string;
  traceId: string;
  parentSpanId: string;
  flags: string;
}

export function parseTraceparent(traceparent: string | null): TraceContext | null {
  if (!traceparent) return null;
  const parts = traceparent.split('-');
  if (parts.length !== 4) return null;
  return {
    version: parts[0],
    traceId: parts[1],
    parentSpanId: parts[2],
    flags: parts[3],
  };
}

export function generateSpanId(): string {
  return Array.from(crypto.getRandomValues(new Uint8Array(8)))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');
}

export function generateTraceId(): string {
  return Array.from(crypto.getRandomValues(new Uint8Array(16)))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');
}

export function rayIdToTraceId(rayId: string): string {
  const hexPart = rayId.split('-')[0];

  if (!/^[0-9a-f]{16}$/i.test(hexPart)) {
    return generateTraceId();
  }

  return (hexPart + hexPart).toLowerCase();
}

export function createTraceparent(traceId: string, spanId: string, flags = '00'): string {
  return `00-${traceId}-${spanId}-${flags}`;
}

export const SpanKind = {
  INTERNAL: 1,
  SERVER: 2,
  CLIENT: 3,
  PRODUCER: 4,
  CONSUMER: 5,
} as const;

interface CollectedSpan {
  traceId: string;
  spanId: string;
  parentSpanId: string | null;
  name: string;
  kind: number;
  startTime: number;
  endTime: number;
  attributes: Record<string, string | number | boolean>;
  status?: { code: number; message?: string };
}

export class MiddlewareTracer {
  private traceId: string;
  private spanId: string;
  private parentSpanId: string | null;
  private startTime: number;
  private env: Env;
  private collectedSpans: CollectedSpan[] = [];
  private waitUntil: (promise: Promise<any>) => void;

  constructor(traceparent: string | null, env: Env, waitUntil: (promise: Promise<any>) => void) {
    this.env = env;
    this.waitUntil = waitUntil;
    this.startTime = Date.now();

    const traceContext = parseTraceparent(traceparent);
    this.traceId = traceContext?.traceId || generateTraceId();
    this.spanId = generateSpanId();
    this.parentSpanId = traceContext?.parentSpanId || null;
  }

  createDownstreamTraceparent(): string {
    return createTraceparent(this.traceId, this.spanId);
  }

  sendSpan(attributes: Record<string, string | number | boolean>) {
    const endTime = Date.now();
    // 收集主 span，稍后发送
    const hasError =
      attributes['middleware.operation'] === 'injection_failed' ||
      attributes['middleware.operation'] === 'redirect_to_login';

    const spanAttributes = { ...attributes };

    this.collectedSpans.unshift({
      traceId: this.traceId,
      spanId: this.spanId,
      parentSpanId: this.parentSpanId,
      name: 'middleware.request',
      kind: SpanKind.SERVER,
      startTime: this.startTime,
      endTime,
      attributes: spanAttributes,
      status: hasError
        ? { code: 2, message: String(attributes['error.message'] || 'Request failed') }
        : { code: 1 },
    });
  }

  // 批量发送所有收集的 spans
  flushSpans() {
    if (this.collectedSpans.length === 0) return;

    const batchPayload = {
      resourceSpans: [
        {
          resource: {
            attributes: [
              { key: 'service.name', value: { stringValue: 'youweb-middleware' } },
              { key: 'service.namespace', value: { stringValue: 'youmind' } },
              {
                key: 'deployment.environment.name',
                value: { stringValue: this.env.ENVIRONMENT || 'unknown' },
              },
            ],
          },
          scopeSpans: [
            {
              scope: { name: 'youweb-middleware' },
              spans: this.collectedSpans.map((span) => {
                const spanData: any = {
                  traceId: span.traceId,
                  spanId: span.spanId,
                  parentSpanId: span.parentSpanId,
                  name: span.name,
                  kind: span.kind,
                  startTimeUnixNano: String(span.startTime * 1000000),
                  endTimeUnixNano: String(span.endTime * 1000000),
                  attributes: Object.entries(span.attributes).map(([key, value]) => ({
                    key,
                    value:
                      typeof value === 'string'
                        ? { stringValue: value }
                        : typeof value === 'boolean'
                          ? { boolValue: value }
                          : { intValue: value },
                  })),
                };

                // 添加状态码
                if (span.status) {
                  spanData.status = {
                    code: span.status.code,
                    message: span.status.message,
                  };
                }

                return spanData;
              }),
            },
          ],
        },
      ],
    };

    // 使用 waitUntil 确保追踪数据发送完成
    if (this.env.NEW_RELIC_LICENSE_KEY) {
      this.waitUntil(
        fetch('https://otlp.nr-data.net/v1/traces', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Api-Key': this.env.NEW_RELIC_LICENSE_KEY,
          },
          body: JSON.stringify(batchPayload),
        }).catch((error) => {
          console.error('Failed to send traces:', error);
        }),
      );
    }

    this.collectedSpans = [];
  }

  // 创建独立的 span 用于特定操作
  async traceOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    attributes?: Record<string, string | number | boolean>,
  ): Promise<T> {
    const operationSpanId = generateSpanId();
    const operationStartTime = Date.now();

    try {
      const result = await operation();

      // 收集成功的操作 span
      const endTime = Date.now();
      this.collectedSpans.push({
        traceId: this.traceId,
        spanId: operationSpanId,
        parentSpanId: this.spanId,
        name: operationName,
        kind: SpanKind.CLIENT, // Outbound operations (API calls, KV, etc.)
        startTime: operationStartTime,
        endTime,
        attributes: {
          'operation.success': true,
          ...attributes,
        },
        status: { code: 1 }, // OTEL Status OK
      });

      return result;
    } catch (error) {
      // 收集失败的操作 span
      const endTime = Date.now();
      this.collectedSpans.push({
        traceId: this.traceId,
        spanId: operationSpanId,
        parentSpanId: this.spanId,
        name: operationName,
        kind: SpanKind.CLIENT, // Outbound operations (API calls, KV, etc.)
        startTime: operationStartTime,
        endTime,
        attributes: {
          'operation.success': false,
          'error.message': String(error),
          ...attributes,
        },
        status: { code: 2, message: String(error) }, // OTEL Status ERROR
      });

      throw error;
    }
  }
}
