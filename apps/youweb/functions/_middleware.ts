/**
 * Cloudflare Pages Functions 中间件
 * 拦截 HTML 响应并注入应用配置
 *
 * 实时配置服务 - 仅注入客户端安全配置 (PUBLIC_ 前缀)
 */

import { MiddlewareTracer } from './tracing';

// Cloudflare Pages Functions types
declare global {
  interface KVNamespace {
    get(key: string, type: 'json'): Promise<Record<string, unknown>>;
    get(key: string): Promise<string | null>;
  }
}

interface Env {
  CONFIG_KV: KVNamespace;
  ENVIRONMENT?: string; // 添加环境变量支持
  NEW_RELIC_LICENSE_KEY?: string; // New Relic API key for tracing
}

export async function onRequest(context: {
  request: Request;
  next: () => Promise<Response>;
  env: Env;
  waitUntil: (promise: Promise<any>) => void;
}): Promise<Response> {
  const { request, next, env, waitUntil } = context;
  const traceparent = request.headers.get('traceparent');

  // 初始化追踪器
  const tracer = new MiddlewareTracer(traceparent, env, waitUntil);

  // 确定 API 主机地址 - 环境感知
  const isProduction = env.ENVIRONMENT === 'production';
  const apiHost = isProduction ? 'em2025.youmind.com' : 'em2025-preview.youmind.com';
  const protocol = 'https';

  let userData: Record<string, unknown> | null = null;
  const responseCookies: string[] = [];

  // 获取原始响应
  const response = await next();

  // 只处理 HTML 响应
  const contentType = response.headers.get('content-type');
  if (!contentType?.includes('text/html')) {
    return response;
  }

  // 对于 HTML 请求，获取用户信息
  if (request.method === 'GET') {
    try {
      // 追踪 youapi 调用
      const userResponse = await tracer.traceOperation(
        'youapi.getCurrentUser',
        () =>
          fetch(`${protocol}://${apiHost}/api/v1/getCurrentUser`, {
            method: 'POST',
            headers: {
              cookie: request.headers.get('cookie') || '',
              'content-type': 'application/json',
              accept: '*/*',
              'x-forwarded-host': apiHost,
              'x-forwarded-proto': protocol,
              host: apiHost,
              origin: `${protocol}://${apiHost}`,
              traceparent: tracer.createDownstreamTraceparent(),
            },
          }),
        {
          'youapi.endpoint': '/api/v1/getCurrentUser',
          'youapi.method': 'POST',
          'youapi.host': apiHost,
        },
      );

      if (userResponse.ok) {
        userData = await userResponse.json();

        // 收集来自 youapi 的 Set-Cookie 头，需要转发给客户端
        const setCookieHeaders =
          userResponse.headers.getSetCookie?.() ||
          userResponse.headers.get('set-cookie')?.split(',') ||
          [];
        responseCookies.push(...setCookieHeaders);

        console.log('✅ User authenticated successfully');
      } else {
        throw new Error('User authentication failed, response not ok');
      }
    } catch (_error) {
      // 重定向到登录页面，带上当前请求路径
      const url = new URL(request.url);
      const requestedPath = url.pathname + url.search;
      const redirectUrl = `/sign-in?next=${encodeURIComponent(requestedPath)}`;

      const redirectResponse = new Response(null, {
        status: 302,
        headers: {
          Location: redirectUrl,
        },
      });

      // 发送重定向的追踪数据
      tracer.sendSpan({
        'http.method': request.method,
        'http.url': request.url,
        'http.status_code': 302,
        'middleware.operation': 'redirect_to_login',
        'redirect.target': redirectUrl,
      });

      // 使用 waitUntil 确保追踪数据发送完成
      tracer.flushSpans();

      return redirectResponse;
    }
  }

  // 读取 HTML 内容一次，避免重复消费响应体
  const html = await response.text();

  try {
    // 追踪 KV 读取操作
    const configData = (await tracer.traceOperation(
      'cloudflare.kv.get',
      () => env.CONFIG_KV.get('app-config', 'json'),
      {
        'cloudflare.kv.namespace': 'CONFIG_KV',
        'cloudflare.kv.key': 'app-config',
        'cloudflare.kv.type': 'json',
      },
    )) as Record<string, unknown> | null;

    if (!configData) {
      console.warn('⚠️ No config found in KV, injecting empty config');
    }

    // 提取客户端安全配置（已在 youconfig worker 中转换类型）
    const clientSafeConfig = (configData?.client as Record<string, unknown>) || {};

    // 注入用户数据和应用配置（与 rsbuild.config.ts 保持一致）
    const scripts = [
      `<script>window.YOUMIND_USER = ${JSON.stringify(userData)};</script>`,
      `<script>window.__APP_CONFIG__ = ${JSON.stringify(clientSafeConfig)};</script>`,
      `<script>window.__TRACE_PARENT__ = ${JSON.stringify(tracer.createDownstreamTraceparent())};</script>`,
    ].join('\n');

    // 注入配置到 HTML
    const injectedHtml = html.replace('</head>', `${scripts}</head>`);

    // 发送成功的追踪数据
    tracer.sendSpan({
      'http.method': request.method,
      'http.url': request.url,
      'http.status_code': response.status,
      'middleware.operation': 'injection_success',
      'user.authenticated': !!userData,
      'config.loaded': !!configData,
    });

    // 使用 waitUntil 确保追踪数据发送完成
    tracer.flushSpans();

    // 准备响应头，包含来自 youapi 的 cookies
    const responseHeaders = new Headers(response.headers);
    responseCookies.forEach((cookie) => {
      responseHeaders.append('Set-Cookie', cookie);
    });

    // 返回注入配置后的响应
    return new Response(injectedHtml, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
  } catch (error) {
    console.error('❌ Config injection failed:', error);

    // 失败时注入空配置，确保应用仍能正常运行（使用已读取的 HTML）
    const fallbackScript = [
      `<script>window.__APP_CONFIG__ = {};</script>`,
      `<script>window.__TRACE_PARENT__ = ${JSON.stringify(tracer.createDownstreamTraceparent())};</script>`,
    ].join('\n');
    const injectedHtml = html.replace('</head>', `${fallbackScript}</head>`);

    // 发送失败的追踪数据
    tracer.sendSpan({
      'http.method': request.method,
      'http.url': request.url,
      'http.status_code': response.status,
      'middleware.operation': 'injection_failed',
      'error.message': String(error),
      'user.authenticated': !!userData,
    });

    // 使用 waitUntil 确保追踪数据发送完成
    tracer.flushSpans();

    // 准备响应头，包含来自 youapi 的 cookies（如果有的话）
    const responseHeaders = new Headers(response.headers);
    responseCookies.forEach((cookie) => {
      responseHeaders.append('Set-Cookie', cookie);
    });

    return new Response(injectedHtml, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
  }
}
