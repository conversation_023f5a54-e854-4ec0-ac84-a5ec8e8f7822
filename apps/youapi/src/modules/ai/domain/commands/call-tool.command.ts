import { ChatCompletionTool } from 'openai/resources/index.js';
import { Subject } from 'rxjs';
import { CompletionStreamChunk } from '@/common/types';
import { ToolCompletionBlock } from '@/modules/chat/domain/message/models/completion-block.vo';
import { Generation } from '../model/generation.entity';

export class CallToolCommand {
  constructor(
    public readonly param: {
      input: Record<string, any>;
      generation: Generation;
      toolDefinition: ChatCompletionTool;
      completionBlock: ToolCompletionBlock;
      subject?: Subject<CompletionStreamChunk<any>>;
    },
  ) {}
}
