import { Logger } from '@nestjs/common';
import { AggregateRoot } from '@nestjs/cqrs';
import { CoreMessage, GenerateTextResult, TextStreamPart, Tool, ToolChoice } from 'ai';
import { ChatPromptClient, LangfuseGenerationClient, LangfuseSpanClient } from 'langfuse';
import { ChatCompletionTool } from 'openai/resources/index.js';
import { Observable, Subject } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { LangfuseTraceService } from '@/common/services/langfuse-trace.service';
import {
  CompletionBlockTypeEnum,
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  DEFAULT_AI_CHAT_MODEL,
  GenerationStatusEnum,
  LLMs,
} from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import {
  CompletionBlock,
  ContentCompletionBlock,
  ReasoningCompletionBlock,
  ToolCompletionBlock,
} from '@/modules/chat/domain/message/models/completion-block.vo';

export interface ImageEditMask {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ImageEditOptions {
  prompt: string;
  size?: '1024x1024' | '1536x1024' | '1024x1536' | 'auto';
  quality?: 'standard' | 'hd';
  mask?: ImageEditMask[];
}

export interface ImageGenerationOptions {
  prompt: string;
  size?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792';
  quality?: 'standard' | 'hd';
  style?: 'vivid' | 'natural';
  n?: number;
}

export class Generation extends AggregateRoot {
  protected readonly logger = new Logger(Generation.name);

  public model: LLMs;
  public temperature: number;
  public tools: ChatCompletionTool[];
  public toolChoice: ToolChoice<Tool>;
  public modelOptions: Record<string, any>;
  public traceMetadata: Record<string, string>;
  public bizArgs: Record<string, any>;

  protected status: GenerationStatusEnum = GenerationStatusEnum.PENDING;
  protected trace: LangfuseGenerationClient | null = null;

  protected prompt: ChatPromptClient;
  public promptName: string;
  public promptVersion: number;
  public promptMessages: CoreMessage[];
  public generatedMessages: CoreMessage[];
  public blocks: CompletionBlock[];

  constructor(param: {
    model?: LLMs;
    temperature?: number;
    tools?: ChatCompletionTool[];
    toolChoice?: ToolChoice<Tool>;
    bizArgs?: Record<string, any>;
    modelOptions?: Record<string, any>;
    traceMetadata?: Record<string, string>;
    prompt?: ChatPromptClient;
    promptMessages?: CoreMessage[];
  }) {
    super();

    this.prompt = param.prompt;
    const promptConfig = param.prompt?.config as {
      model?: LLMs;
      temperature?: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
    };

    this.model = param.model || promptConfig?.model || DEFAULT_AI_CHAT_MODEL;
    this.temperature = param.temperature || promptConfig?.temperature || 0.5;
    this.tools = param.tools || [];
    this.toolChoice = param.toolChoice || this.tools.length > 0 ? 'auto' : 'none';

    const topP = param.modelOptions?.topP || promptConfig?.topP || 1;
    const frequencyPenalty =
      param.modelOptions?.frequencyPenalty || promptConfig?.frequencyPenalty || 0;
    const presencePenalty =
      param.modelOptions?.presencePenalty || promptConfig?.presencePenalty || 0;

    this.bizArgs = param.bizArgs || {};
    this.traceMetadata = param.traceMetadata || {};
    this.modelOptions = {
      ...param.modelOptions,
      ...(topP ? { topP } : {}),
      ...(frequencyPenalty ? { frequencyPenalty } : {}),
      ...(presencePenalty ? { presencePenalty } : {}),
    };
    this.promptName = param.prompt?.name || '';
    this.promptVersion = param.prompt?.version || 1;
    this.promptMessages = param.promptMessages || [];
    this.blocks = [];
  }

  public setStatus(status: GenerationStatusEnum) {
    this.status = status;
  }

  public addGeneratedMessage(message: CoreMessage) {
    this.generatedMessages.push(message);
  }

  public getToolBlockByToolId(toolId: string): ToolCompletionBlock | null {
    return (
      (this.blocks.find(
        (block) =>
          block.type === CompletionBlockTypeEnum.TOOL &&
          (block as ToolCompletionBlock).toolId === toolId,
      ) as ToolCompletionBlock) || null
    );
  }

  private chatMessageId: string | null = null;
  public getChatMessageId(): string | null {
    return this.chatMessageId;
  }
  public setChatMessageId(messageId: string) {
    this.chatMessageId = messageId;
    return this;
  }

  public setBizArgs(bizArgs: Record<string, any>) {
    this.bizArgs = {
      ...this.bizArgs,
      ...bizArgs,
    };
    return this;
  }

  /**
   * 录入 Completion Block
   * TODO: CompletionBlock 入库
   * @param observable
   */
  public processTextStream(
    observable: Observable<TextStreamPart<any>>,
    emitSubject?: Subject<CompletionStreamChunk<any>>,
  ) {
    const subject = emitSubject || new Subject<CompletionStreamChunk<any>>();
    const traceService = ApplicationContext.getProvider<LangfuseTraceService>(LangfuseTraceService);
    const spanMap = new Map<string, LangfuseSpanClient>();

    observable
      .pipe(
        finalize(() => {
          const fullMessages: CoreMessage[] = this.blocks.flatMap((block) =>
            block.toCoreMessages(),
          );

          const messages = [];
          for (const message of fullMessages) {
            const lastMessage = messages[messages.length - 1];
            // 在 content 长度小于 5 时，合并同类 role 的 message
            if (
              lastMessage &&
              lastMessage.role === message.role &&
              lastMessage.content.length + message.content.length < 5
            ) {
              lastMessage.content.push(...message.content);
            } else {
              messages.push(message);
            }
          }

          this.generatedMessages = messages;
        }),
      )
      .subscribe(async (chunk) => {
        const getLastBlock = () => {
          if (this.blocks.length === 0) {
            return null;
          }

          return this.blocks[this.blocks.length - 1];
        };

        if (chunk.type === 'text-delta') {
          let current: ContentCompletionBlock | null = getLastBlock() as ContentCompletionBlock;
          if (!current || current.type !== CompletionBlockTypeEnum.CONTENT) {
            current = ContentCompletionBlock.createNew({
              messageId: this.chatMessageId,
              data: '',
            });

            this.blocks.push(current);

            subject.next({
              mode: CompletionStreamModeEnum.INSERT,
              data: current.toCompletionStreamChunk(),
              dataType: 'CompletionBlock',
            });
          }

          (current as ContentCompletionBlock).appendContent(chunk.textDelta);

          subject.next({
            mode: CompletionStreamModeEnum.APPEND_STRING,
            data: chunk.textDelta,
            path: 'data',
            targetId: current.id,
            targetType: 'CompletionBlock',
          });

          return;
        }

        if (
          chunk.type === 'reasoning' ||
          chunk.type === 'redacted-reasoning' ||
          chunk.type === 'reasoning-signature'
        ) {
          let current: ReasoningCompletionBlock | null = getLastBlock() as ReasoningCompletionBlock;
          if (!current || current.type !== CompletionBlockTypeEnum.REASONING) {
            current = ReasoningCompletionBlock.createNew({
              messageId: this.chatMessageId,
              data: '',
            });

            this.blocks.push(current);

            subject.next({
              mode: CompletionStreamModeEnum.INSERT,
              data: current.toCompletionStreamChunk(),
              dataType: 'CompletionBlock',
            });
          }

          if (chunk.type === 'reasoning') {
            (current as ReasoningCompletionBlock).appendContent(chunk.textDelta);

            subject.next({
              mode: CompletionStreamModeEnum.APPEND_STRING,
              data: chunk.textDelta,
              path: 'data',
              targetId: current.id,
              targetType: 'CompletionBlock',
            });
          } else if (chunk.type === 'redacted-reasoning') {
            (current as ReasoningCompletionBlock).appendRedactedReasoning(chunk.data);
          } else if (chunk.type === 'reasoning-signature') {
            (current as ReasoningCompletionBlock).setSignature(chunk.signature);
          }

          return;
        }

        if (
          chunk.type === 'tool-call' ||
          chunk.type === 'tool-call-delta' ||
          chunk.type === 'tool-call-streaming-start' ||
          chunk.type === 'tool-result'
        ) {
          let current: ToolCompletionBlock | null = this.getToolBlockByToolId(chunk.toolCallId);

          if (!current) {
            current = ToolCompletionBlock.createNew({
              messageId: this.chatMessageId,
              toolId: chunk.toolCallId,
              toolName: chunk.toolName,
            });

            this.blocks.push(current);

            subject.next({
              mode: CompletionStreamModeEnum.INSERT,
              data: current.toCompletionStreamChunk(),
              dataType: 'CompletionBlock',
            });
            spanMap.set(
              chunk.toolCallId,
              await traceService.addSpan({
                name: 'tool-call-' + chunk.toolCallId,
                input: {},
              }),
            );
          }

          if (chunk.type === 'tool-call-delta') {
            current.appendPartialToolArguments(chunk.argsTextDelta);

            subject.next({
              mode: CompletionStreamModeEnum.APPEND_JSON,
              data: chunk.argsTextDelta,
              path: 'tool_arguments',
              targetId: current.id,
              targetType: 'CompletionBlock',
            });
          } else if (chunk.type === 'tool-call') {
            current.setToolArguments(chunk.args as Record<string, unknown>);
            current.startExecution();
          } else if (chunk.type === 'tool-result') {
            const toolResult = (chunk as any).jsonResult;
            if (!chunk.result.startsWith('ERROR')) {
              current.completeExecution(toolResult, chunk.result);

              subject.next({
                mode: CompletionStreamModeEnum.REPLACE,
                data: current.toolResult,
                path: 'tool_result',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
            } else {
              current.failExecution(toolResult, chunk.result);

              subject.next({
                mode: CompletionStreamModeEnum.REPLACE,
                data: current.toolResult,
                path: 'extra.error',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
            }

            spanMap.get(chunk.toolCallId)?.update({
              input: current.toolArguments,
              output: {
                result: current.toolResult,
                response: current.toolResponse,
              },
              statusMessage: current.status,
            });
          }

          return;
        }

        // TODO: 处理错误
        if (chunk.type === 'error') {
          subject.error(chunk.error);
          this.status = GenerationStatusEnum.FAILED;

          return;
        }
      });

    return subject.asObservable();
  }

  public processTextResult(result: GenerateTextResult<any, any>) {
    const subject = new Subject<CompletionStreamChunk<any>>();

    // add block
    const block = ContentCompletionBlock.createNew({
      messageId: this.chatMessageId,
      data: result.text,
    });
    this.blocks.push(block);

    // add messages
    this.addGeneratedMessage({
      role: 'assistant',
      content: result.text,
    });

    // emit event
    subject.next({
      mode: CompletionStreamModeEnum.INSERT,
      data: block.toCompletionStreamChunk(),
      dataType: 'CompletionBlock',
    });

    return subject.asObservable();
  }

  static fromCurrentGeneration(current: Generation): Generation {
    return new Generation({
      model: current.model,
      temperature: current.temperature,
      tools: current.tools,
      toolChoice: current.toolChoice,
      bizArgs: current.bizArgs,
      modelOptions: current.modelOptions,
      traceMetadata: current.traceMetadata,
      prompt: current.prompt,
      promptMessages: [...current.promptMessages, ...current.generatedMessages],
    });
  }
}

// export class ImageGeneration extends Generation {
//   public imageOptions: ImageGenerationOptions | null = null;
//   public imageEditOptions: ImageEditOptions | null = null;
//   public imageUrl: string | null = null;
//   public operation: 'generate' | 'edit' = 'generate';

//   constructor(param: {
//     model?: LLMs;
//     temperature?: number;
//     traceMetadata?: Record<string, string>;
//     prompt?: ChatPromptClient;
//     imageOptions?: ImageGenerationOptions;
//     imageEditOptions?: ImageEditOptions;
//     imageUrl?: string;
//     operation?: 'generate' | 'edit';
//   }) {
//     super({
//       model: param.model || LLMs.GPT_IMAGE_1,
//       temperature: param.temperature || 0,
//       traceMetadata: param.traceMetadata,
//       prompt: param.prompt,
//     });

//     this.imageOptions = param.imageOptions || null;
//     this.imageEditOptions = param.imageEditOptions || null;
//     this.imageUrl = param.imageUrl || null;
//     this.operation = param.operation || 'generate';
//   }

//   static createForGeneration(
//     options: ImageGenerationOptions,
//     param?: {
//       traceMetadata?: Record<string, string>;
//       prompt?: ChatPromptClient;
//     },
//   ): ImageGeneration {
//     return new ImageGeneration({
//       ...param,
//       imageOptions: options,
//       operation: 'generate',
//     });
//   }

//   static createForEdit(
//     imageUrl: string,
//     options: ImageEditOptions,
//     param?: {
//       traceMetadata?: Record<string, string>;
//       prompt?: ChatPromptClient;
//     },
//   ): ImageGeneration {
//     return new ImageGeneration({
//       ...param,
//       imageUrl,
//       imageEditOptions: options,
//       operation: 'edit',
//     });
//   }
// }
