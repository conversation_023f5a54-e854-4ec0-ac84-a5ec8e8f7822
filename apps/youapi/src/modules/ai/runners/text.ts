import { Logger } from '@nestjs/common';
import { GenerateTextResult, generateText, streamText } from 'ai';
import { ApplicationContext } from '@/common/utils/application-context';
import { Generation } from '../domain/model/generation.entity';
import { PromptService } from '../prompt/index.service';
import { ModelProviderService } from '../providers/index.service';
import { streamToObservable } from '../utils/toObservable';
import { BaseRunner, type GenerateOptions } from './base';

/**
 * TextRun<PERSON> wraps the Generation domain model with execution logic
 * Tracing is handled by decorators and subscriptions
 */
export class TextRunner extends BaseRunner {
  protected readonly logger = new Logger(TextRunner.name);

  public async generateOnce(options?: GenerateOptions): Promise<GenerateTextResult<any, any>> {
    const { useCache = true } = options || {};
    if (!this.currentGeneration) {
      throw new Error('Generation is required');
    }

    const model = ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getLanguageModel(this.model, { useCache });
    const traceName = this.promptName || 'text-generation';
    await this.startTrace(model, traceName);

    // Start generation tracking - use prompt name if available
    try {
      const result = await generateText(this.assembleApiParameters(model));

      // End generation with success
      const usageReport = this.buildUsageReport(result.usage);
      await this.traceService.updateGeneration({
        ...usageReport,
        output: result.text,
        metadata: {
          finishReason: result.finishReason,
          success: true,
        },
      });

      this.currentGeneration.processTextResult(result);

      return result;
    } catch (error) {
      await this.traceService.updateGeneration({
        metadata: {
          success: false,
          error: error.message,
          errorStack: error.stack,
        },
      });
      throw error;
    }
  }

  public async generateStream(options?: GenerateOptions) {
    const { useCache = true } = options || {};
    if (!this.currentGeneration) {
      throw new Error('Generation is required');
    }

    const model = ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getLanguageModel(this.model, { useCache });

    const traceName = this.promptName || 'stream-text-generation';
    const traceNode = await this.startTrace(model, traceName, options);

    const generator = streamText({
      ...this.assembleApiParameters(model),
      onFinish: ({ usage, finishReason }) => {
        const usageReport = this.buildUsageReport(usage);
        traceNode.end({
          ...usageReport,
          metadata: {
            success: true,
            finishReason,
          },
        });
      },
      onError: ({ error }) => {
        traceNode.end({
          metadata: {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined,
          },
        });
      },
    });

    const streamTextObservable = streamToObservable(generator);
    this.currentGeneration.processTextStream(streamTextObservable);

    return streamTextObservable;
  }

  static fromPrompt(promptName: string, variables: Record<string, any>) {
    const promptService = ApplicationContext.getProvider<PromptService>(PromptService);
    const { prompt, promptMessages } = promptService.getPromptAndMessages(promptName, variables);
    const generation = new Generation({
      prompt,
      promptMessages,
    });
    const runner = new TextRunner();
    runner.addGeneration(generation);
    return runner;
  }
}
