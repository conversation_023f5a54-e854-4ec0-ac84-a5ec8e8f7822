import { LanguageModelV1 } from '@ai-sdk/provider';
import { Logger } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { CoreMessage, generateText, Tool, ToolChoice } from 'ai';
import { LangfuseGenerationClient } from 'langfuse-core';
import { ChatCompletionTool } from 'openai/resources/index.js';
import { LangfuseTraceService } from '@/common/services/langfuse-trace.service';
import { LLMs, MODEL_DEFINITION } from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import { UsageRecordDomainService } from '@/domain/usage-record';
import { ToolCompletionBlock } from '@/modules/chat/domain/message/models/completion-block.vo';
import { CallToolCommand } from '../domain/commands/call-tool.command';
import { Generation } from '../domain/model/generation';

export interface GenerateOptions {
  useCache?: boolean;
  inheritTrace?: boolean;
}

/**
 * 带 trace 的基础 Runner
 */
export class BaseRunner {
  protected readonly logger = new Logger(BaseRunner.name);
  protected generations: Generation[] = [];
  protected currentGeneration: Generation;
  protected commandBus: CommandBus;
  protected traceService: LangfuseTraceService;
  protected usageRecordDomainService: UsageRecordDomainService;

  constructor() {
    this.traceService = ApplicationContext.getProvider<LangfuseTraceService>(LangfuseTraceService);
    this.usageRecordDomainService =
      ApplicationContext.getProvider<UsageRecordDomainService>(UsageRecordDomainService);
    this.commandBus = ApplicationContext.getProvider<CommandBus>(CommandBus);
  }

  addGeneration(generation: Generation) {
    this.generations.push(generation);
    this.currentGeneration = generation;
    return this;
  }

  get model(): LLMs {
    return this.currentGeneration.model;
  }

  setModel(model: LLMs) {
    this.currentGeneration.model = model;
    return this;
  }

  get temperature(): number {
    return this.currentGeneration.temperature;
  }

  setTemperature(temperature: number) {
    this.currentGeneration.temperature = temperature;
    return this;
  }

  setRegenerate(regenerate: boolean) {
    if (regenerate) {
      this.currentGeneration.temperature = Math.min(this.currentGeneration.temperature * 2, 1);
    }
    return this;
  }

  get tools(): ChatCompletionTool[] {
    return this.currentGeneration.tools;
  }

  get toolChoice(): ToolChoice<Tool> {
    return this.currentGeneration.toolChoice;
  }

  get topP(): number {
    return this.currentGeneration.modelOptions.topP || 1;
  }

  get frequencyPenalty(): number {
    return this.currentGeneration.modelOptions.frequencyPenalty || 0;
  }

  get presencePenalty(): number {
    return this.currentGeneration.modelOptions.presencePenalty || 0;
  }

  get modelOptions(): {
    topP?: number;
    temperature?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  } {
    return this.currentGeneration.modelOptions;
  }

  get traceMetadata(): Record<string, string> {
    return this.currentGeneration.traceMetadata;
  }

  updateMetadata(metadata: Record<string, string>) {
    this.currentGeneration.traceMetadata = {
      ...this.currentGeneration.traceMetadata,
      ...metadata,
    };
    return this;
  }

  get promptName(): string {
    return this.currentGeneration.promptName;
  }

  get promptMessages(): CoreMessage[] {
    return this.currentGeneration.promptMessages;
  }

  protected async startTrace(
    model: LanguageModelV1,
    name?: string,
    options?: GenerateOptions,
  ): Promise<LangfuseGenerationClient> {
    const generation = await this.traceService.addGeneration(
      {
        ...this.buildGenerationParams(model, name || this.promptName),
        metadata: {
          ...this.traceMetadata,
          useCache: options?.useCache,
        },
      },
      options?.inheritTrace,
    );
    // TODO: 要 append input 不是替换 input
    await this.traceService.updateTrace({
      input: this.promptMessages,
    });
    return generation;
  }

  /**
   * Common method to assemble generation parameters for tracing
   */
  protected buildGenerationParams(model: LanguageModelV1, name?: string) {
    const input = this.currentGeneration?.promptMessages || this.currentGeneration?.bizArgs;
    const params = {
      name: name || this.promptName || 'text-generation',
      model: model.modelId,
      input: input,
      modelParameters: {
        provider: model.provider,
        ...this.modelOptions,
        ...(this.tools.length > 0 && {
          tools: this.tools.map((t) => t.function.name).join(', '),
          toolChoice: typeof this.toolChoice === 'string' ? this.toolChoice : 'required',
        }),
      },
    };

    return params;
  }

  /**
   * Common method to extract and format usage information for tracing
   */
  protected buildUsageReport(usage: any) {
    if (!usage) return undefined;

    // Calculate cost details if model definition is available
    const modelDef = MODEL_DEFINITION.get(this.model);
    let costDetails: any;
    let usageInfo: any;
    let usageDetails: any;

    if (modelDef) {
      costDetails = {
        input:
          (usage.promptTokens / 1000000) * modelDef.input_per_mil +
          ((usage.cachedInputTokens || 0) / 1000000) * modelDef.cached_input_per_mil,
        output:
          ((usage.completionTokens + (usage.reasoningTokens || 0)) / 1000000) *
          modelDef.output_per_mil,
        cache_read_input_tokens: usage.cachedInputTokens || 0,
        total: 0,
      };
      costDetails.total = costDetails.input + costDetails.output;
    }

    usageInfo = {
      promptTokens: usage.promptTokens || usage.inputTokens,
      completionTokens: usage.completionTokens || usage.outputTokens,
      totalTokens: usage.totalTokens,
    };

    usageDetails = {
      input: usage.promptTokens || usage.inputTokens,
      output: usage.completionTokens || usage.outputTokens,
      input_audio_tokens: 0,
      input_cache_tokens: usage.cachedInputTokens || 0,
      output_audio_tokens: 0,
      output_reasoning_tokens: usage.reasoningTokens || 0,
      output_accepted_prediction_tokens: 0,
      output_rejected_prediction_tokens: 0,
    };

    return {
      usageInfo,
      usageDetails,
      ...(costDetails && { costDetails }),
    };
  }

  protected assembleApiParameters(model: LanguageModelV1): Parameters<typeof generateText>[0] {
    if (!this.promptMessages?.length) {
      throw new Error('Prompt messages are required');
    }

    return {
      model,
      messages: this.promptMessages,
      ...this.modelOptions,
      // Only pass tools if they exist and aren't empty
      ...(this.tools && this.tools.length > 0
        ? {
            tools: this.tools.reduce(
              (acc, toolDefinition) => {
                acc[toolDefinition.function.name] = {
                  parameters: toolDefinition.function.parameters,
                  description: toolDefinition.function.description,
                  execute: async (input, { toolCallId }) => {
                    return await this.commandBus.execute(
                      new CallToolCommand({
                        toolDefinition,
                        input,
                        generation: this.currentGeneration,
                        completionBlock: ToolCompletionBlock.createNew({
                          messageId: '',
                          toolId: toolCallId,
                          toolName: toolDefinition.function.name,
                          toolArguments: input,
                          toolResult: {},
                          toolResponse: '',
                        }),
                      }),
                    );
                  },
                } as Tool;
                return acc;
              },
              {} as Record<string, Tool>,
            ),
            toolChoice: this.toolChoice as ToolChoice<Tool>,
          }
        : {}),
    };
  }
}
