import { Logger } from '@nestjs/common';
import { ObjectStreamPart, StreamObjectResult, StreamTextResult, TextStreamPart, Tool } from 'ai';
import OpenAI from 'openai';
import { filter, from, map, Observable, shareReplay } from 'rxjs';

const logger = new Logger('StreamToObservable');

export function streamToObservable<T extends Record<string, Tool> = Record<string, Tool>>(
  response: StreamTextResult<T, any>,
): Observable<TextStreamPart<T>>;
export function streamToObservable<T extends Record<string, Tool> = Record<string, Tool>, R = any>(
  response: StreamObjectResult<T, R, any>,
): Observable<ObjectStreamPart<T>>;
export function streamToObservable<T extends Record<string, Tool> = Record<string, Tool>, R = any>(
  response: StreamTextResult<T, any> | StreamObjectResult<T, R, any>,
): Observable<TextStreamPart<T> | ObjectStreamPart<T>> {
  // 返回可多次消费的 observable
  return from(response.fullStream).pipe(
    shareReplay({ bufferSize: Number.MAX_SAFE_INTEGER, refCount: false }),
  ) as Observable<TextStreamPart<T> | ObjectStreamPart<T>>;
}

export function toThrottledObservable<T extends Record<string, Tool> = Record<string, Tool>>(
  observable: Observable<TextStreamPart<T> | ObjectStreamPart<T>>,
  throttleTime: number = 1000,
): Observable<TextStreamPart<T> | ObjectStreamPart<T>> {
  return new Observable<TextStreamPart<T> | ObjectStreamPart<T>>((subscriber) => {
    let buffer: Array<TextStreamPart<T> | ObjectStreamPart<T>> = [];
    let timer: ReturnType<typeof setTimeout> | null = null;
    let currentType: string | null = null;

    const flushBuffer = () => {
      if (buffer.length === 0) return;

      const mergedMessage = mergeMessages(buffer);
      if (mergedMessage) {
        subscriber.next(mergedMessage);
      }

      buffer = [];
      currentType = null;
      if (timer) {
        clearTimeout(timer);
        timer = null;
      }
    };

    const mergeMessages = (
      messages: Array<TextStreamPart<T> | ObjectStreamPart<T>>,
    ): TextStreamPart<T> | ObjectStreamPart<T> | null => {
      if (messages.length === 0) return null;
      if (messages.length === 1) return messages[0];

      const firstMessage = messages[0];
      const messageType = firstMessage.type;

      switch (messageType) {
        case 'text-delta': {
          // Only merge text-delta messages
          const textMessages = messages.filter((msg) => msg.type === 'text-delta') as Array<
            TextStreamPart<T> & { type: 'text-delta'; textDelta: string }
          >;
          if (textMessages.length === 0) return messages[0];

          const lastMessage = textMessages[textMessages.length - 1];
          return {
            ...lastMessage,
            textDelta: textMessages.map((msg) => msg.textDelta).join(''),
          } as TextStreamPart<T>;
        }

        case 'tool-call-delta': {
          // Only merge tool-call-delta messages
          const toolMessages = messages.filter((msg) => msg.type === 'tool-call-delta') as Array<
            TextStreamPart<T> & { type: 'tool-call-delta'; argsTextDelta: string }
          >;
          if (toolMessages.length === 0) return messages[0];

          const lastMessage = toolMessages[toolMessages.length - 1];
          return {
            ...lastMessage,
            argsTextDelta: toolMessages.map((msg) => msg.argsTextDelta || '').join(''),
          } as TextStreamPart<T>;
        }

        default:
          // For other message types, return the last message (no meaningful merge)
          return messages[messages.length - 1];
      }
    };

    const startThrottleTimer = () => {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        flushBuffer();
      }, throttleTime);
    };

    const isMergeableType = (type: string): boolean => {
      return ['text-delta', 'tool-call-delta'].includes(type);
    };

    const subscription = observable.subscribe({
      next: (part) => {
        // If this is a new type or the first message
        if (currentType === null || part.type !== currentType) {
          // Flush existing buffer if it has different type
          if (buffer.length > 0) {
            flushBuffer();
          }

          // For non-mergeable types, emit immediately
          if (!isMergeableType(part.type)) {
            subscriber.next(part);
            return;
          }

          // Start new buffer for mergeable types
          currentType = part.type;
          buffer = [part];
          startThrottleTimer();
        } else {
          // Same type as current buffer, add to buffer
          buffer.push(part);
          // Reset timer for the throttle window
          startThrottleTimer();
        }
      },
      error: (err) => {
        if (timer) clearTimeout(timer);
        subscriber.error(err);
      },
      complete: () => {
        flushBuffer();
        subscriber.complete();
      },
    });

    return () => {
      if (timer) clearTimeout(timer);
      subscription.unsubscribe();
    };
  }).pipe(shareReplay({ bufferSize: Number.MAX_SAFE_INTEGER, refCount: false }));
}

export function toOpenaiCompatibleObservable<T extends Record<string, Tool> = Record<string, Tool>>(
  observable: Observable<TextStreamPart<T>>,
): Observable<OpenAI.ChatCompletionChunk> {
  const messageId = 'chatcmpl-' + Date.now();

  return observable.pipe(
    // Convert each stream part to OpenAI format
    map((part: TextStreamPart<T>): OpenAI.ChatCompletionChunk | null => {
      try {
        switch (part.type) {
          case 'text-delta':
            return {
              id: messageId,
              object: 'chat.completion.chunk',
              created: Math.floor(Date.now() / 1000),
              model: '',
              choices: [
                {
                  index: 0,
                  delta: {
                    content: part.textDelta,
                    role: 'assistant' as const,
                  },
                  finish_reason: null,
                  logprobs: null,
                },
              ],
              usage: null,
              system_fingerprint: null,
            };

          case 'tool-call':
            return {
              id: messageId,
              object: 'chat.completion.chunk',
              created: Math.floor(Date.now() / 1000),
              model: '',
              choices: [
                {
                  index: 0,
                  delta: {
                    tool_calls: [
                      {
                        index: 0,
                        id: part.toolCallId,
                        type: 'function' as const,
                        function: {
                          name: part.toolName,
                          arguments: JSON.stringify(part.args),
                        },
                      },
                    ],
                  },
                  finish_reason: null,
                  logprobs: null,
                },
              ],
              usage: null,
              system_fingerprint: null,
            };

          case 'tool-call-delta':
            // Handle streaming tool call arguments
            return {
              id: messageId,
              object: 'chat.completion.chunk',
              created: Math.floor(Date.now() / 1000),
              model: '',
              choices: [
                {
                  index: 0,
                  delta: {
                    tool_calls: [
                      {
                        index: 0,
                        function: {
                          arguments: part.argsTextDelta,
                        },
                      },
                    ],
                  },
                  finish_reason: null,
                  logprobs: null,
                },
              ],
              usage: null,
              system_fingerprint: null,
            };

          case 'tool-result':
            // Tool execution result - typically not streamed to client
            return null;

          case 'finish':
            return {
              id: messageId,
              object: 'chat.completion.chunk',
              created: Math.floor(Date.now() / 1000),
              model: '',
              choices: [
                {
                  index: 0,
                  delta: {},
                  finish_reason:
                    part.finishReason === 'content-filter'
                      ? 'content_filter'
                      : (part.finishReason as any),
                  logprobs: null,
                },
              ],
              usage: part.usage
                ? {
                    prompt_tokens: part.usage.promptTokens,
                    completion_tokens: part.usage.completionTokens,
                    total_tokens: part.usage.totalTokens,
                  }
                : null,
              system_fingerprint: null,
            };

          case 'error':
            logger.error('Stream error:', part.error);
            return null;

          default:
            logger.warn('Ignore mapping chunk type:', part.type);
            return null;
        }
      } catch (error) {
        logger.error('Error processing stream part:', error, part);
        return null;
      }
    }),
    // Filter out null values
    filter((chunk): chunk is OpenAI.ChatCompletionChunk => chunk !== null),
  ) as Observable<OpenAI.ChatCompletionChunk>;
}

export function streamToOpenaiCompatibleObservable<
  T extends Record<string, Tool> = Record<string, Tool>,
>(response: StreamTextResult<T, any>): Observable<OpenAI.ChatCompletionChunk> {
  return toOpenaiCompatibleObservable(streamToObservable(response));
}

export function observableToSse(observable: Observable<any>): Observable<string> {
  return observable.pipe(
    map((data) => {
      if (data === null) logger.warn('SSE data is null');
      return `data: ${JSON.stringify(data)}\n\n`;
    }),
  );
}
