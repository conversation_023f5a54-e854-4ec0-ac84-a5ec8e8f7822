import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { TextRunnerService } from '@/modules/ai/runners';
import { GenThoughtTitleCommand } from '../../commands/thought/gen-thought-title.command';

@Injectable()
@CommandHandler(GenThoughtTitleCommand)
export class GenThoughtTitleHandler implements ICommandHandler<GenThoughtTitleCommand> {
  constructor(private readonly textRunnerService: TextRunnerService) {}

  async execute(command: GenThoughtTitleCommand): Promise<{ title: string }> {
    const { content, useCache } = command;

    const runner = this.textRunnerService.getThoughtAutoGenerateTitleRunner({ content });
    const res = await runner.generateOnce({ useCache });
    return { title: res.text || '' };
  }
}
