import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import type { CompletionStreamSupportedDataType } from '@/common/types';
import {
  ChatModeEnum,
  ChatOriginTypeEnum,
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  CompletionStreamModeEnum,
  MessageModeEnum,
  MessageRoleEnum,
  MessageStatusEnum,
} from '@/common/types';
import { AtReferenceDto, EditCommandDto, ShortcutDto, UseToolsDto } from './chat.dto';

/**
 * Base completion stream chunk DTO
 */
export abstract class BaseCompletionStreamChunkDto {
  @ApiProperty({
    enum: CompletionStreamModeEnum,
    description: 'The mode of the stream chunk operation',
    example: CompletionStreamModeEnum.INSERT,
  })
  @IsEnum(CompletionStreamModeEnum)
  mode: CompletionStreamModeEnum;
}

export class CompletionChatDto {
  @ApiProperty({
    description: 'The ID of the chat',
    example: 'chat-123',
  })
  @IsUUID()
  id: string;

  @ApiProperty({})
  @IsUUID()
  creatorId: string;

  @ApiProperty({
    description: 'The creation time of the chat',
    example: '2021-01-01T00:00:00.000Z',
  })
  @IsString()
  createdAt: string;

  @ApiProperty({
    description: 'The update time of the chat',
    example: '2021-01-01T00:00:00.000Z',
  })
  @IsString()
  updatedAt: string;

  @ApiProperty({
    description: 'The mode of the chat',
    example: 'chat',
  })
  @IsEnum(ChatModeEnum)
  mode: ChatModeEnum;

  @ApiProperty({
    description: 'The title of the chat',
    example: 'My Chat',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'The origin of the chat',
    example: 'chat',
  })
  @IsEnum(ChatOriginTypeEnum)
  origin: ChatOriginTypeEnum;

  @ApiPropertyOptional({
    description: 'The board ID of the chat',
    example: 'board-123',
  })
  @IsUUID()
  boardId: string;
}

export class CompletionMessageDto {
  @ApiProperty({
    description: 'The ID of the message',
    example: 'msg-123',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'The ID of the chat',
    example: 'chat-123',
  })
  @IsUUID()
  chatId: string;

  @ApiProperty({
    description: 'The creation time of the chat',
    example: '2021-01-01T00:00:00.000Z',
  })
  @IsString()
  createdAt: string;

  @ApiProperty({
    description: 'The update time of the chat',
    example: '2021-01-01T00:00:00.000Z',
  })
  @IsString()
  updatedAt: string;

  @ApiProperty({
    description: 'The status of the message',
    example: 'user',
  })
  @IsEnum(MessageRoleEnum)
  role: MessageRoleEnum;

  @ApiProperty({
    description: 'The status of the message',
    example: 'success',
  })
  @IsEnum(MessageStatusEnum)
  status: MessageStatusEnum;

  @ApiPropertyOptional({
    description: 'The content of the message',
    example: 'Hello world',
  })
  @IsString()
  content: string;

  @ApiPropertyOptional({
    description: 'The origin of the message',
    example: 'chat',
  })
  @IsEnum(ChatOriginTypeEnum)
  origin: ChatOriginTypeEnum;

  @ApiPropertyOptional({
    description: 'The selection of the message',
    example: 'Hello world',
  })
  @IsString()
  selection: string;

  @ApiPropertyOptional({
    description: 'The at references of the message',
    example: [
      {
        type: 'at',
        id: '123',
        name: 'John Doe',
      },
    ],
  })
  @IsArray()
  atReferences: AtReferenceDto[];

  @ApiPropertyOptional({
    description: 'The board ID of the message',
    example: 'board-123',
  })
  @IsUUID()
  boardId: string;

  @ApiPropertyOptional({
    description: 'Tools configuration',
    type: [UseToolsDto],
  })
  @Type(() => UseToolsDto)
  tools?: UseToolsDto;

  @ApiPropertyOptional({ description: 'Edit command' })
  command?: EditCommandDto;

  @ApiProperty({ description: 'Message mode', enum: MessageModeEnum })
  mode: MessageModeEnum;

  @ApiPropertyOptional({ description: 'Shortcut information', type: [UseToolsDto] })
  @Type(() => ShortcutDto)
  shortcut?: ShortcutDto;
}

export class CompletionBlockDto {
  @ApiProperty({
    description: 'The ID of the block',
    example: 'block-123',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'The ID of the chat',
    example: 'chat-123',
  })
  @IsUUID()
  messageId: string;

  @ApiProperty({
    description: 'The type of the block',
    example: 'text',
  })
  @IsEnum(CompletionBlockTypeEnum)
  type: CompletionBlockTypeEnum;

  @ApiProperty({
    description: 'The status of the block',
    example: 'success',
  })
  @IsEnum(CompletionBlockStatusEnum)
  status: CompletionBlockStatusEnum;

  @ApiProperty({
    description: 'The extra of the block',
    example: 'Hello world',
  })
  @IsObject()
  extra: object;

  @ApiPropertyOptional({
    description: 'The data of the block',
    example: 'Hello world',
  })
  @IsString()
  data: string;

  @ApiPropertyOptional({
    description: 'The tool ID of the block',
    example: 'tool-123',
  })
  @IsUUID()
  toolId: string;

  @ApiPropertyOptional({
    description: 'The tool name of the block',
    example: 'tool-123',
  })
  @IsString()
  toolName: string;

  @ApiPropertyOptional({
    description: 'The tool arguments of the block',
    example: 'Hello world',
  })
  @IsObject()
  toolArguments: object;

  @ApiPropertyOptional({
    description: 'The tool result of the block',
    example: 'Hello world',
  })
  @IsObject()
  toolResult: object;

  @ApiPropertyOptional({
    description: 'The tool response of the block',
    example: 'Hello world',
  })
  @IsString()
  toolResponse: string;
}

export type CompletionDataDto = CompletionChatDto | CompletionMessageDto | CompletionBlockDto;

/**
 * Insert completion stream chunk DTO
 * Used when inserting new data into the stream
 */
export class CompletionStreamInsertChunkDto extends BaseCompletionStreamChunkDto {
  @ApiProperty({
    enum: CompletionStreamModeEnum,
    enumName: 'CompletionStreamModeEnum',
    example: CompletionStreamModeEnum.INSERT,
  })
  declare mode: CompletionStreamModeEnum.INSERT;

  @ApiProperty({
    enum: ['Chat', 'Message', 'CompletionBlock'],
    description: 'The type of data being inserted',
    example: 'Message',
  })
  @IsString()
  dataType: CompletionStreamSupportedDataType;

  @ApiProperty({
    description: 'The data payload being inserted',
    oneOf: [
      { $ref: '#/components/schemas/CompletionChatDto' },
      { $ref: '#/components/schemas/CompletionMessageDto' },
      { $ref: '#/components/schemas/CompletionBlockDto' },
    ],
  })
  @ValidateNested()
  data: CompletionDataDto;
}

/**
 * Replace completion stream chunk DTO
 * Used when replacing existing data in the stream
 */
export class CompletionStreamReplaceChunkDto extends BaseCompletionStreamChunkDto {
  @ApiProperty({
    enum: CompletionStreamModeEnum,
    enumName: 'CompletionStreamModeEnum',
    example: CompletionStreamModeEnum.REPLACE,
  })
  declare mode: CompletionStreamModeEnum.REPLACE;

  @ApiProperty({
    enum: ['Chat', 'Message', 'CompletionBlock'],
    description: 'The type of target being replaced',
    example: 'Message',
  })
  @IsString()
  targetType: CompletionStreamSupportedDataType;

  @ApiProperty({
    description: 'The ID of the target being replaced',
    example: 'msg-123',
  })
  @IsUUID()
  targetId: string;

  @ApiProperty({
    description: 'The new data to replace with',
    example: { status: 'completed' },
  })
  data: CompletionDataDto;

  @ApiProperty({
    description: 'The path of the field being replaced',
    example: 'status',
  })
  @IsString()
  path: string;
}

/**
 * Append string completion stream chunk DTO
 * Used when appending string data to existing content
 */
export class CompletionStreamAppendStringChunkDto extends BaseCompletionStreamChunkDto {
  @ApiProperty({
    enum: CompletionStreamModeEnum,
    enumName: 'CompletionStreamModeEnum',
    example: CompletionStreamModeEnum.APPEND_STRING,
  })
  declare mode: CompletionStreamModeEnum.APPEND_STRING;

  @ApiProperty({
    enum: ['Chat', 'Message', 'CompletionBlock'],
    description: 'The type of target being appended to',
  })
  @IsString()
  targetType: CompletionStreamSupportedDataType;

  @ApiProperty({
    description: 'The ID of the target being appended to',
    example: 'msg-123',
  })
  @IsString()
  targetId: string;

  @ApiProperty({
    description: 'The string data to append',
    example: ' Additional text content',
  })
  @IsString()
  data: string;

  @ApiProperty({
    description: 'The path of the field being appended to',
    example: 'content',
  })
  @IsString()
  path: string;
}

/**
 * Append JSON completion stream chunk DTO
 * Used when appending JSON data to existing content
 */
export class CompletionStreamAppendJsonChunkDto extends BaseCompletionStreamChunkDto {
  @ApiProperty({
    enum: CompletionStreamModeEnum,
    enumName: 'CompletionStreamModeEnum',
    example: CompletionStreamModeEnum.APPEND_JSON,
  })
  declare mode: CompletionStreamModeEnum.APPEND_JSON;

  @ApiProperty({
    enum: ['Chat', 'Message', 'CompletionBlock'],
    description: 'The type of target being appended to',
    example: 'CompletionBlock',
  })
  @IsString()
  targetType: CompletionStreamSupportedDataType;

  @ApiProperty({
    description: 'The ID of the target being appended to',
    example: 'block-123',
  })
  @IsString()
  targetId: string;

  @ApiProperty({
    description: 'The JSON string data to append',
    example: '{"result": "success"}',
  })
  @IsString()
  data: string;

  @ApiProperty({
    description: 'The path of the field being appended to',
    example: 'toolResult',
  })
  @IsString()
  path: string;
}

/**
 * Union type for all completion stream chunk DTOs
 * This represents the discriminated union of all possible stream chunk types
 */
export type CompletionStreamChunkDto =
  | CompletionStreamInsertChunkDto
  | CompletionStreamReplaceChunkDto
  | CompletionStreamAppendStringChunkDto
  | CompletionStreamAppendJsonChunkDto;

/**
 * SSE Event wrapper for completion stream chunks
 * This represents the actual SSE event format sent to clients
 */
export class CompletionStreamEventDto {
  @ApiProperty({
    description: 'Event ID for SSE',
    example: '1640995200000',
    required: false,
  })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({
    description: 'Event type for SSE',
    example: 'completion-chunk',
    default: 'completion-chunk',
    required: false,
  })
  @IsOptional()
  @IsString()
  event?: string;

  @ApiProperty({
    description: 'The completion stream chunk data',
    oneOf: [
      { $ref: '#/components/schemas/CompletionStreamInsertChunkDto' },
      { $ref: '#/components/schemas/CompletionStreamReplaceChunkDto' },
      { $ref: '#/components/schemas/CompletionStreamAppendStringChunkDto' },
      { $ref: '#/components/schemas/CompletionStreamAppendJsonChunkDto' },
    ],
  })
  data: CompletionStreamChunkDto;
}
