import { omit } from 'lodash';
import { filter, map, type Observable } from 'rxjs';
import {
  CompletionBlockTypeEnum,
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  MessageStatusEnum,
  StreamDataTypeEnum,
  StreamMessage,
} from '@/common/types';

/**
 * Converts CompletionStreamChunk to StreamMessage format
 * Migrated from youapp src/lib/domain/chat/index.ts
 */
export function v2ObservableToStreamMessage(
  observable: Observable<CompletionStreamChunk<any>>,
): Observable<StreamMessage<any>> {
  return observable.pipe(
    map((chunk): StreamMessage<any> | null => {
      // Handle INSERT mode - when new blocks are created
      if (chunk.mode === CompletionStreamModeEnum.INSERT) {
        if (chunk.dataType === 'CompletionBlock') {
          const block = omit(chunk.data, ['tool_id', 'tool_response']);

          // Handle reasoning blocks
          if (block.type === CompletionBlockTypeEnum.REASONING) {
            return {
              type: StreamDataTypeEnum.CHUNKED_DATA,
              parentDataType: 'Message',
              parentId: block.message_id, // Assume message_id is available
              chunkPath: 'reasoning_begin_at',
              chunkData: new Date(block.created_at).getTime().toString(),
            } as StreamMessage<any>;
          }

          // Handle tool blocks with status messages
          if (block.type === CompletionBlockTypeEnum.TOOL) {
            const message = getToolMessage(block);
            if (message) {
              return {
                type: StreamDataTypeEnum.STATUS_UPDATE,
                status: MessageStatusEnum.ING,
                message,
              } as StreamMessage<any>;
            }
          }
        }
        return null;
      }

      // Handle REPLACE mode - when existing data is updated
      if (chunk.mode === CompletionStreamModeEnum.REPLACE) {
        if (chunk.targetType === 'Message') {
          if (chunk.path === 'trace_id') {
            return {
              type: StreamDataTypeEnum.TRACE_ID,
              trace_id: chunk.data,
            };
          }

          if (chunk.path === 'error') {
            return {
              type: StreamDataTypeEnum.ERROR,
              error: chunk.data,
            };
          }
        }

        if (chunk.targetType === 'CompletionBlock') {
          if (chunk.path === 'tool_result') {
            // This would need access to the block index and conversion logic
            // For now, return a placeholder structure
            return {
              type: StreamDataTypeEnum.CHUNKED_DATA,
              parentDataType: 'Message',
              parentId: chunk.targetId, // Approximate
              chunkPath: 'events.0', // Would need proper index
              chunkData: chunk.data,
            };
          }

          if (chunk.path === 'updated_at') {
            // Handle reasoning end timestamp
            return {
              type: StreamDataTypeEnum.CHUNKED_DATA,
              parentDataType: 'Message',
              parentId: chunk.targetId,
              chunkPath: 'reasoning_end_at',
              chunkData: new Date(chunk.data).getTime().toString(),
            };
          }

          if (chunk.path === 'extra.error') {
            return {
              type: StreamDataTypeEnum.ERROR,
              error: chunk.data,
            };
          }
        }
      }

      // Handle APPEND_STRING mode - when text content is streamed
      if (chunk.mode === CompletionStreamModeEnum.APPEND_STRING) {
        if (chunk.targetType === 'CompletionBlock' && chunk.path === 'data') {
          // Need block type to determine content vs reasoning
          // For content blocks
          return {
            data: chunk.data,
            type: StreamDataTypeEnum.CONTENT,
            contentType: 'content', // or 'reasoning' based on block type
          };
        }
      }

      // Handle APPEND_JSON mode - when structured data is streamed
      if (chunk.mode === CompletionStreamModeEnum.APPEND_JSON) {
        if (chunk.targetType === 'CompletionBlock' && chunk.path === 'tool_arguments') {
          // Tool arguments are being updated - would need additional logic
          // to parse and validate JSON before emitting status updates
          return null;
        }
      }

      return null;
    }),
    filter((sse) => sse !== null),
  );
}

/**
 * Get status message for tool blocks
 */
function getToolMessage(block: any): string {
  if (['image_edit', 'IMAGE_GENERATE', 'DIAGRAM_GENERATE'].includes(block.tool_name)) {
    return 'Creating image';
  }
  if (block.tool_name === 'google_search' && block.tool_arguments?.query) {
    return 'Searching ' + (block.tool_arguments?.query || '');
  }
  if (block.tool_name === 'library_search') {
    return 'Searching in board ' + (block.tool_arguments?.query || '');
  }
  if (block.tool_name === 'CREATE_SNIP_BY_URL') {
    return 'Creating Snip';
  }
  return '';
}
