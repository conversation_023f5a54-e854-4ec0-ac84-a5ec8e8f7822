import { CoreMessage } from 'ai';
import { ChatCompletionTool } from 'openai/resources/index.js';
import type { Observable } from 'rxjs';
import { ReplaySubject } from 'rxjs';
import {
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  MessageStatusEnum,
  TOOL_TYPES,
} from '@/common/types';
import { Generation } from '@/modules/ai/domain/model/generation';
import { Chat } from '../../domain/chat/models/chat.entity';
import { SendMessageCommand } from '../commands/send-message.command';
import { ChatRunner } from './base';

export class Ask<PERSON><PERSON>ner extends ChatRunner {
  protected _promptName = 'ai-ask-chat-prompt';

  constructor(
    protected readonly chat: Chat,
    protected readonly userId: string,
  ) {
    super(chat, userId);
    this._prompt = this.promptService.fetchPrompt({ name: this._promptName });
  }

  protected async preparePromptMessages(): Promise<CoreMessage[]> {
    const user = await this.userRepository.getById(this.userId);
    const userPreference = await this.userPreferenceRepository.getByUserId(this.userId);
    const language = userPreference.aiResponseLanguage;
    const assistantMessage = this.chat.getLastAssistantMessage();
    const promptVariables: Record<string, string> = {
      model: assistantMessage.model,
      userName: user.name,
      language,
      currentTime: new Date().toLocaleString(),
    };

    return [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: this.chat.getLastUserMessage().content,
          },
        ],
      },
    ];
  }

  protected extractToolDefinitions(command: SendMessageCommand): ChatCompletionTool[] {
    const tools = [];
    if (command.param.tools?.[TOOL_TYPES.GOOGLE_SEARCH]?.useTool === 'auto') {
      // TODO: 改为从 @jialiang tool service 里获取 tool 定义
      tools.push({
        type: 'function',
        function: {
          name: 'google_search',
          description: 'Search the web for information',
          parameters: {
            type: 'object',
          },
        },
      });
    }
    return tools;
  }

  async generate<T>(command: SendMessageCommand): Promise<Observable<CompletionStreamChunk<any>>> {
    if (!this.chat.getLastAssistantMessage()) {
      throw new Error('Chat has no assistant message');
    }
    if (!this.chat.getLastUserMessage()) {
      throw new Error('Chat has no user message');
    }

    const subject = new ReplaySubject<CompletionStreamChunk<any>>();
    subject.next({
      mode: CompletionStreamModeEnum.INSERT,
      dataType: 'Chat',
      data: this.chat.toCompletionStreamChunk(),
    });

    if (this.chat.getLastUserMessage().isNew) {
      await this.messageRepository.save(this.chat.getLastUserMessage());
      this.chat.getLastUserMessage().commit();
    }
    subject.next({
      mode: CompletionStreamModeEnum.INSERT,
      dataType: 'Message',
      data: this.chat.getLastUserMessage().toCompletionStreamChunk(),
    });

    if (this.chat.getLastAssistantMessage().isNew) {
      await this.messageRepository.save(this.chat.getLastAssistantMessage());
      this.chat.getLastAssistantMessage().commit();
    }
    subject.next({
      mode: CompletionStreamModeEnum.INSERT,
      dataType: 'Message',
      data: this.chat.getLastAssistantMessage().toCompletionStreamChunk(),
    });
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'Message',
      targetId: this.chat.getLastAssistantMessage().id,
      path: 'trace_id',
      data: this.chat.getLastAssistantMessage().traceId,
    });

    const assistantMessage = this.chat.getLastAssistantMessage();
    const promptMessages = await this.preparePromptMessages();
    this.currentGeneration = new Generation({
      model: assistantMessage.model,
      tools: this.extractToolDefinitions(command),
      toolChoice: 'auto',
      prompt: this._prompt,
      promptMessages,
      traceMetadata: this.chat.getTraceMetadata(),
    });
    this.currentGeneration.setChatMessageId(assistantMessage.id).setBizArgs({
      chatId: this.chat.id,
      userId: this.userId,
      messageId: assistantMessage.id,
    });

    assistantMessage.updateStatus(MessageStatusEnum.ING);
    await this.messageRepository.save(assistantMessage);
    assistantMessage.commit();

    // Start text generation
    await this.streamTextGeneration<T>(subject);

    return subject.asObservable();
  }
}
