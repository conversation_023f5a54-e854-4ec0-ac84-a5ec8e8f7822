import { LanguageModelV1, streamText, TextStreamPart } from 'ai';
import { ChatPromptClient } from 'langfuse-core';
import { merge, ReplaySubject, Subject } from 'rxjs';
import {
  CompletionStreamChunk,
  GenerationStatusEnum,
  LLMs,
  MODEL_DEFINITION,
} from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import { CallToolCommand } from '@/modules/ai/domain/commands/call-tool.command';
import { Generation } from '@/modules/ai/domain/model/generation.entity';
import { PromptService } from '@/modules/ai/prompt/index.service';
import { ModelProviderService } from '@/modules/ai/providers/index.service';
import { BaseRunner } from '@/modules/ai/runners/base';
import { streamToObservable } from '@/modules/ai/utils/toObservable';
import { UserRepository } from '@/modules/iam/repositories/user.repository';
import { UserPreferenceRepository } from '@/modules/iam/repositories/user-preference.repository';
import { Chat } from '../../domain/chat/models/chat.entity';
import { ChatRepository } from '../../repositories/chat.repository';
import { MessageRepository } from '../../repositories/message.repository';

export class ChatRunner extends BaseRunner {
  protected _promptName = '';
  protected promptService: PromptService;
  protected _prompt: ChatPromptClient;
  protected userRepository: UserRepository;
  protected userPreferenceRepository: UserPreferenceRepository;
  protected chatRepository: ChatRepository;
  protected messageRepository: MessageRepository;

  constructor(
    protected readonly chat: Chat,
    protected readonly userId: string,
  ) {
    super();
    this.promptService = ApplicationContext.getProvider<PromptService>(PromptService);
    this.userRepository = ApplicationContext.getProvider<UserRepository>(UserRepository);
    this.userPreferenceRepository =
      ApplicationContext.getProvider<UserPreferenceRepository>(UserPreferenceRepository);
    this.chatRepository = ApplicationContext.getProvider<ChatRepository>(ChatRepository);
    this.messageRepository = ApplicationContext.getProvider<MessageRepository>(MessageRepository);
  }

  protected assembleApiParameters(model: LanguageModelV1): Parameters<typeof streamText>[0] {
    const params = super.assembleApiParameters(model);
    if (!params.providerOptions) {
      params.providerOptions = {};
    }

    const messages = this.promptMessages;

    // 加上 anthropic cacheControl
    params.messages = [
      ...messages.slice(0, -1),
      {
        ...messages[messages.length - 1],
        providerOptions: {
          anthropic: {
            cacheControl: { type: 'ephemeral' },
          },
        },
      },
    ];
    // 开启手动 tool 执行
    for (const tool in params.tools) {
      delete params.tools[tool].execute;
    }

    const modelDefinition = MODEL_DEFINITION.get(this.model);
    if (modelDefinition.type === 'reasoning') {
      delete params.temperature;
    }

    // 模型参数修改
    if (model.modelId === LLMs.GPT_4O_MINI) {
      params.presencePenalty = 0.8;
      params.frequencyPenalty = 0.8;
    }
    if (modelDefinition.from === 'openai') {
      if (!params.providerOptions.openai) {
        params.providerOptions.openai = {};
      }

      params.providerOptions.openai.parallelToolCalls = false;
      if (modelDefinition.type === 'reasoning') {
        params.providerOptions.openai.reasoningEffort = 'medium';
      }
    }
    if (modelDefinition.from === 'anthropic') {
      if (!params.providerOptions.anthropic) {
        params.providerOptions.anthropic = {};
      }

      if (modelDefinition.type === 'reasoning') {
        params.providerOptions.anthropic.thinking = {
          type: 'enabled',
          budgetTokens: modelDefinition.output_token_limit / 2,
        };
      }
    }

    // 加接口失败重试
    params.maxRetries = 2;
    return params;
  }

  protected async streamTextGeneration<T>(subject: Subject<CompletionStreamChunk<T>>) {
    const model = ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getLanguageModel(this.model, { useCache: false });
    const traceNode = await this.startTrace(model, this._promptName, {
      inheritTrace: true,
    });

    let onFirstChunk = false;
    const params = this.assembleApiParameters(model);

    // fake reasoning 控制流
    const controlChannel = new ReplaySubject<TextStreamPart<any>>();
    const modelDefinition = MODEL_DEFINITION.get(this.model);
    if (modelDefinition.extra?.fake_reasoning) {
      controlChannel.next({
        type: 'reasoning',
        textDelta: '',
      });
    }

    const generator = streamText({
      ...params,
      toolCallStreaming: true,
      onChunk: () => {
        if (onFirstChunk) return;

        onFirstChunk = true;
        traceNode.update({
          completionStartTime: new Date(),
        });
      },
      onFinish: async (result) => {
        const { usage, finishReason } = result;

        if (finishReason === 'tool-calls') {
          const toolCalls = result.toolCalls;
          for (const toolCall of toolCalls) {
            await this.commandBus.execute(
              new CallToolCommand({
                subject, // 仅限透传消息，其他消息通过 processTextStream 转换
                input: toolCall.args,
                generation: this.currentGeneration,
                toolDefinition: this.currentGeneration.tools[toolCall.toolName],
                completionBlock: this.currentGeneration.getToolBlockByToolId(toolCall.toolCallId),
              }),
            );
          }

          traceNode.end({
            metadata: {
              success: true,
              finishReason,
            },
          });

          this.currentGeneration = Generation.fromCurrentGeneration(this.currentGeneration);
          await this.streamTextGeneration(subject);
          return;
        }

        subject.complete();
        this.currentGeneration.setStatus(GenerationStatusEnum.SUCCESS);

        const usageReport = this.buildUsageReport(usage);
        traceNode.end({
          ...usageReport,
          metadata: {
            success: true,
            finishReason,
          },
        });
      },
      onError: ({ error }) => {
        subject.error(error);
        this.currentGeneration.setStatus(GenerationStatusEnum.FAILED);

        traceNode.end({
          metadata: {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined,
          },
        });
      },
    });

    const streamTextObservable = streamToObservable(generator);
    this.currentGeneration.processTextStream(merge(controlChannel, streamTextObservable), subject);
  }
}
