/**
 * CompletionBlock 值对象 - 完成块值对象
 * 表示消息生成过程中的不同类型的完成块
 *
 * 遵循 DDD 原则，作为 Message 聚合中的值对象
 */

import { CoreAssistantMessage, CoreMessage } from 'ai';
import { uuidv7 } from 'uuidv7';
import { CompletionBlockStatusEnum, CompletionBlockTypeEnum } from '@/common/types';
import { camelToSnake } from '@/common/utils';
import { completionBlocks } from '@/dao/db/public.schema';
import { CompletionBlockUpdatedEvent } from '../events/completion-block-updated.event';

export type CompletionBlockDO = typeof completionBlocks.$inferSelect & {
  status: CompletionBlockStatusEnum;
  extra?: Record<string, unknown>;
  toolResult?: Record<string, unknown>;
  toolArguments?: Record<string, unknown>;
};

/**
 * 基础完成块值对象
 */
export abstract class CompletionBlock {
  public readonly id: string;
  public readonly createdAt: Date;
  public updatedAt: Date;
  public deletedAt: Date | null;
  public readonly type: CompletionBlockTypeEnum;
  public readonly messageId: string;
  public data: string;
  public status: CompletionBlockStatusEnum;
  public extra: Record<string, unknown>;
  public isModified: boolean = false;

  constructor(
    data: {
      id: string;
      createdAt: Date;
      updatedAt: Date;
      deletedAt?: Date | null;
      type: CompletionBlockTypeEnum;
      data: string;
      status: CompletionBlockStatusEnum;
      messageId: string;
      extra?: Record<string, unknown>;
    },
    public readonly isNew: boolean = false,
  ) {
    this.id = data.id;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
    this.deletedAt = data.deletedAt || null;
    this.type = data.type;
    this.data = data.data;
    this.status = data.status;
    this.messageId = data.messageId;
    this.extra = data.extra || {};
  }

  /**
   * 业务规则：检查块是否已完成
   */
  isCompleted(): boolean {
    return this.status === CompletionBlockStatusEnum.DONE;
  }

  /**
   * 业务规则：检查块是否正在生成
   */
  isGenerating(): boolean {
    return this.status === CompletionBlockStatusEnum.ING;
  }

  /**
   * 业务规则：检查工具是否正在执行
   */
  isExecuting(): boolean {
    return this.status === CompletionBlockStatusEnum.EXECUTING;
  }

  /**
   * 业务规则：检查块是否出错
   */
  hasError(): boolean {
    return this.status === CompletionBlockStatusEnum.ERROR;
  }

  /**
   * 业务规则：检查块是否被中止
   */
  isAborted(): boolean {
    return this.status === CompletionBlockStatusEnum.ABORT;
  }

  /**
   * 更新状态
   */
  updateStatus(newStatus: CompletionBlockStatusEnum): CompletionBlockUpdatedEvent {
    const oldStatus = this.status;
    this.status = newStatus;
    this.updatedAt = new Date();
    this.isModified = true;

    return new CompletionBlockUpdatedEvent(this.id, this.messageId, 'status', oldStatus, newStatus);
  }

  /**
   * 设置额外数据
   */
  setExtra(key: string, value: unknown): void {
    this.extra[key] = value;
    this.updatedAt = new Date();
    this.isModified = true;
  }

  /**
   * 获取块内容
   */
  getContent(): string {
    return this.data;
  }

  toDO(): CompletionBlockDO {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      deletedAt: this.deletedAt,
      type: this.type,
      status: this.status,
      messageId: this.messageId,
      data: this.data,
      extra: this.extra,
      toolId: null,
      toolName: null,
      toolArguments: null,
      toolResult: null,
      toolResponse: null,
      toolGenerateElapsedMs: null,
      toolExecuteElapsedMs: null,
    };
  }

  toCompletionStreamChunk(): object {
    return camelToSnake({
      id: this.id,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      type: this.type,
      status: this.status,
      messageId: this.messageId,
      extra: this.extra,
    });
  }

  abstract toCoreMessages(): CoreMessage[];
}

/**
 * 内容完成块 - 存储LLM生成的文本内容
 */
export class ContentCompletionBlock extends CompletionBlock {
  constructor(
    data: {
      id: string;
      createdAt: Date;
      updatedAt: Date;
      deletedAt?: Date | null;
      status: CompletionBlockStatusEnum;
      messageId: string;
      data: string;
      extra?: Record<string, unknown>;
    },
    isNew: boolean = false,
  ) {
    super(
      {
        ...data,
        type: CompletionBlockTypeEnum.CONTENT,
      },
      isNew,
    );
  }

  /**
   * 设置完整内容
   */
  setContent(content: string): CompletionBlockUpdatedEvent {
    const oldData = this.data;
    this.data = content;
    this.updatedAt = new Date();
    this.isModified = true;

    return new CompletionBlockUpdatedEvent(
      this.id,
      this.messageId,
      'content_set',
      oldData,
      content,
    );
  }

  /**
   * 追加内容
   */
  appendContent(content: string): CompletionBlockUpdatedEvent {
    const oldData = this.data;
    this.data += content;
    this.updatedAt = new Date();
    this.isModified = true;

    return new CompletionBlockUpdatedEvent(
      this.id,
      this.messageId,
      'content_append',
      oldData,
      this.data,
    );
  }

  toCompletionStreamChunk(): object {
    const base = super.toCompletionStreamChunk();
    return {
      ...base,
      data: this.data,
    };
  }

  toCoreMessages(): CoreMessage[] {
    return [
      {
        role: 'assistant',
        content: [
          {
            type: 'text',
            text: this.data,
          },
        ],
      },
    ];
  }

  static fromDO(data: CompletionBlockDO): ContentCompletionBlock {
    const now = new Date();
    return new ContentCompletionBlock(
      {
        id: data.id || uuidv7(),
        createdAt: now,
        updatedAt: now,
        status: CompletionBlockStatusEnum.ING,
        messageId: data.messageId,
        data: data.data || '',
      },
      !!data.id,
    );
  }

  static createNew(data: { messageId: string; data?: string }): ContentCompletionBlock {
    return new ContentCompletionBlock(
      {
        id: uuidv7(),
        createdAt: new Date(),
        updatedAt: new Date(),
        status: CompletionBlockStatusEnum.ING,
        messageId: data.messageId,
        data: data.data || '',
      },
      true,
    );
  }
}

/**
 * 推理完成块 - 存储LLM的推理过程
 */
export class ReasoningCompletionBlock extends CompletionBlock {
  constructor(
    data: {
      id: string;
      createdAt: Date;
      updatedAt: Date;
      deletedAt?: Date | null;
      status: CompletionBlockStatusEnum;
      messageId: string;
      data: string;
      extra?: Record<string, unknown>;
    },
    isNew: boolean = false,
  ) {
    super(
      {
        ...data,
        type: CompletionBlockTypeEnum.REASONING,
      },
      isNew,
    );
  }

  /**
   * 设置完整内容
   */
  setContent(content: string): CompletionBlockUpdatedEvent {
    const oldData = this.data;
    this.data = content;
    this.updatedAt = new Date();
    this.isModified = true;

    return new CompletionBlockUpdatedEvent(
      this.id,
      this.messageId,
      'content_set',
      oldData,
      content,
    );
  }

  /**
   * 追加内容
   */
  appendContent(content: string): CompletionBlockUpdatedEvent {
    const oldData = this.data;
    this.data += content;
    this.updatedAt = new Date();
    this.isModified = true;

    return new CompletionBlockUpdatedEvent(
      this.id,
      this.messageId,
      'content_append',
      oldData,
      this.data,
    );
  }

  appendRedactedReasoning(content: string): CompletionBlockUpdatedEvent {
    if (!this.extra?.redactedReasoning) {
      if (!this.extra) {
        this.extra = {};
      }
      this.extra.redactedReasoning = '';
    }

    const oldData = this.extra.redactedReasoning;
    this.extra.redactedReasoning += content;
    this.updatedAt = new Date();
    this.isModified = true;

    return new CompletionBlockUpdatedEvent(
      this.id,
      this.messageId,
      'redacted_reasoning_append',
      oldData,
      this.extra.redactedReasoning,
    );
  }

  setSignature(signature: string): CompletionBlockUpdatedEvent {
    if (!this.extra?.signature) {
      if (!this.extra) {
        this.extra = {};
      }
      this.extra.signature = '';
    }

    const oldData = this.extra.signature;
    this.extra.signature = signature;
    this.updatedAt = new Date();
    this.isModified = true;

    return new CompletionBlockUpdatedEvent(
      this.id,
      this.messageId,
      'signature_set',
      oldData,
      this.extra.signature,
    );
  }

  toCompletionStreamChunk(): object {
    const base = super.toCompletionStreamChunk();
    return {
      ...base,
      data: this.data,
    };
  }

  toCoreMessages(): CoreMessage[] {
    const parts: CoreAssistantMessage['content'] = [
      {
        type: 'reasoning',
        text: this.data,
        ...(this.extra?.signature ? { signature: this.extra.signature as string } : {}),
      },
    ];
    if (this.extra?.redactedReasoning) {
      parts.push({
        type: 'redacted-reasoning',
        data: this.extra.redactedReasoning as string,
      });
    }

    return [
      {
        role: 'assistant',
        content: parts,
      },
    ];
  }

  static fromDO(data: CompletionBlockDO): ReasoningCompletionBlock {
    const now = new Date();
    return new ReasoningCompletionBlock(
      {
        id: data.id || uuidv7(),
        createdAt: now,
        updatedAt: now,
        status: CompletionBlockStatusEnum.ING,
        messageId: data.messageId,
        data: data.data || '',
      },
      !!data.id,
    );
  }

  static createNew(data: { messageId: string; data?: string }): ReasoningCompletionBlock {
    return new ReasoningCompletionBlock(
      {
        id: uuidv7(),
        createdAt: new Date(),
        updatedAt: new Date(),
        status: CompletionBlockStatusEnum.ING,
        messageId: data.messageId,
        data: data.data || '',
      },
      true,
    );
  }
}

/**
 * 工具完成块 - 存储工具调用的相关信息
 */
export class ToolCompletionBlock extends CompletionBlock {
  public toolId: string;
  public toolName: string;
  public toolArguments: Record<string, unknown>;
  public toolResult: Record<string, unknown>;
  public toolResponse: string;
  public toolGenerateElapsedMs?: number;
  public toolExecuteElapsedMs?: number;
  public isModified: boolean = false;

  constructor(
    data: {
      id: string;
      createdAt: Date;
      updatedAt: Date;
      deletedAt?: Date | null;
      status: CompletionBlockStatusEnum;
      messageId: string;
      toolId: string;
      toolName: string;
      toolArguments: Record<string, unknown>;
      toolResult: Record<string, unknown>;
      toolResponse: string;
      toolGenerateElapsedMs?: number;
      toolExecuteElapsedMs?: number;
      extra?: Record<string, unknown>;
    },
    public readonly isNew: boolean = false,
  ) {
    super({
      ...data,
      data: null,
      type: CompletionBlockTypeEnum.TOOL,
    });
    this.toolId = data.toolId;
    this.toolName = data.toolName;
    this.toolArguments = data.toolArguments;
    this.toolResult = data.toolResult;
    this.toolResponse = data.toolResponse;
    this.toolGenerateElapsedMs = data.toolGenerateElapsedMs;
    this.toolExecuteElapsedMs = data.toolExecuteElapsedMs;
  }

  /**
   * 设置工具参数
   */
  setToolArguments(arguments_: Record<string, unknown>): CompletionBlockUpdatedEvent {
    if (this.tmpToolArguments) return;
    const oldArguments = this.toolArguments;
    this.toolArguments = arguments_;
    this.updatedAt = new Date();
    this.isModified = true;

    return new CompletionBlockUpdatedEvent(
      this.id,
      this.messageId,
      'tool_arguments',
      oldArguments,
      arguments_,
    );
  }

  private tmpToolArguments: string = '';
  appendPartialToolArguments(arguments_: string) {
    this.tmpToolArguments += arguments_;
  }

  /**
   * 开始执行工具
   */
  private startGenerateTimestamp: number = 0;
  startExecution(): CompletionBlockUpdatedEvent {
    if (this.tmpToolArguments) {
      this.toolArguments = JSON.parse(this.tmpToolArguments);
      this.tmpToolArguments = '';
    }
    this.startGenerateTimestamp = Date.now();
    this.toolGenerateElapsedMs = this.startGenerateTimestamp - this.createdAt.getTime();
    return this.updateStatus(CompletionBlockStatusEnum.EXECUTING);
  }

  /**
   * 完成工具执行
   */
  completeExecution(
    result: Record<string, unknown>,
    response: string,
  ): CompletionBlockUpdatedEvent[] {
    const events: CompletionBlockUpdatedEvent[] = [];

    const oldResult = this.toolResult;
    this.toolResult = result;
    this.toolResponse = response;
    this.toolExecuteElapsedMs = Date.now() - this.startGenerateTimestamp;
    this.updatedAt = new Date();
    this.isModified = true;

    const event = new CompletionBlockUpdatedEvent(
      this.id,
      this.messageId,
      'tool_result',
      oldResult,
      result,
    );
    events.push(event);

    // 更新状态为完成
    events.push(this.updateStatus(CompletionBlockStatusEnum.DONE));

    return events;
  }

  failExecution(
    error: Record<string, unknown>,
    errorMessage: string,
  ): CompletionBlockUpdatedEvent[] {
    const events: CompletionBlockUpdatedEvent[] = [];

    const oldResult = this.toolResult;
    this.toolResult = error;
    this.toolResponse = errorMessage;
    this.toolExecuteElapsedMs = Date.now() - this.startGenerateTimestamp;
    this.updatedAt = new Date();
    this.isModified = true;

    const event = new CompletionBlockUpdatedEvent(
      this.id,
      this.messageId,
      'tool_result',
      oldResult,
      error,
    );
    events.push(event);

    // 更新状态为完成
    events.push(this.updateStatus(CompletionBlockStatusEnum.ERROR));

    return events;
  }

  toDO(): CompletionBlockDO {
    const baseProps = super.toDO();
    return {
      ...baseProps,
      toolId: this.toolId,
      toolName: this.toolName,
      toolArguments: this.toolArguments,
      toolResult: this.toolResult,
      toolResponse: this.toolResponse,
      toolGenerateElapsedMs: this.toolGenerateElapsedMs,
      toolExecuteElapsedMs: this.toolExecuteElapsedMs,
    };
  }

  toCompletionStreamChunk(): object {
    const base = super.toCompletionStreamChunk();
    return {
      ...base,
      ...camelToSnake({
        toolId: this.toolId,
        toolName: this.toolName,
        toolArguments: this.toolArguments,
        toolResult: this.toolResult,
        toolResponse: this.toolResponse,
      }),
    };
  }

  toCoreMessages(): CoreMessage[] {
    const messages: CoreMessage[] = [
      {
        role: 'assistant',
        content: [
          {
            type: 'tool-call',
            toolCallId: this.toolId,
            toolName: this.toolName,
            args: this.toolArguments,
          },
        ],
      },
    ];

    if (this.toolResult) {
      messages.push({
        role: 'tool',
        content: [
          {
            type: 'tool-result',
            result: this.toolResult,
            isError: this.status === CompletionBlockStatusEnum.ERROR,
            toolName: this.toolName,
            toolCallId: this.toolId,
          },
        ],
      });
    }

    return messages;
  }

  /**
   * Repo 方法
   */
  static fromDO(data: CompletionBlockDO): ToolCompletionBlock {
    const now = new Date();
    return new ToolCompletionBlock(
      {
        id: data.id || uuidv7(),
        createdAt: now,
        updatedAt: now,
        status: CompletionBlockStatusEnum.ING,
        messageId: data.messageId,
        toolId: data.toolId,
        toolName: data.toolName,
        toolArguments: data.toolArguments || {},
        toolResult: data.toolResult || {},
        toolResponse: data.toolResponse || '',
      },
      !!data.id,
    );
  }

  static createNew(data: {
    messageId: string;
    toolId: string;
    toolName: string;
    toolArguments?: Record<string, unknown>;
    toolResult?: Record<string, unknown>;
    toolResponse?: string;
  }): ToolCompletionBlock {
    return new ToolCompletionBlock(
      {
        id: uuidv7(),
        createdAt: new Date(),
        updatedAt: new Date(),
        status: CompletionBlockStatusEnum.ING,
        messageId: data.messageId,
        toolId: data.toolId,
        toolName: data.toolName,
        toolArguments: data.toolArguments || {},
        toolResult: data.toolResult || {},
        toolResponse: data.toolResponse || '',
      },
      true,
    );
  }
}

export class CompletionBlockFactory {
  fromDO(data: CompletionBlockDO): CompletionBlock {
    switch (data.type) {
      case CompletionBlockTypeEnum.CONTENT:
        return new ContentCompletionBlock(data, !!data.id);
      case CompletionBlockTypeEnum.REASONING:
        return new ReasoningCompletionBlock(data, !!data.id);
      case CompletionBlockTypeEnum.TOOL:
        return new ToolCompletionBlock(data, !!data.id);
      default:
        throw new Error(`Unknown completion block type: ${data.type}`);
    }
  }
}
