/**
 * Message 领域实体 - 聊天消息实体
 * 封装消息相关的业务逻辑和业务规则
 *
 * 使用抽象类设计模式：
 * - Message: 抽象基类，包含所有消息的共同属性和方法
 * - UserMessage: 用户消息实现，简单的文本消息
 * - AssistantMessage: 助手消息实现，支持 CompletionBlock 和复杂逻辑
 *
 * 遵循 DDD 原则，作为 Chat 聚合中的实体
 */

import { AggregateRoot } from '@nestjs/cqrs';
import { uuidv4 } from 'uuidv7';
import {
  ChatBoardOrigin,
  ChatOriginTypeEnum,
  CompletionBlockTypeEnum,
  DEFAULT_AI_CHAT_MODEL,
  LLMs,
  MessageContext,
  MessageContextEnum,
  MessageModeEnum,
  MessageRoleEnum,
  MessageStatusEnum,
  RestErrorInfo,
  UserMessage as UserMessageType,
} from '@/common/types';
import { camelToSnake, snakeToCamel } from '@/common/utils';
import { messages } from '@/dao/db/public.schema';
import {
  AtReferenceDto,
  ChatOriginDto,
  EditCommandDto,
  ShortcutDto,
  UseToolsDto,
} from '@/modules/chat/dto/chat.dto';
import { MessageDeletedEvent } from '../events/message-deleted.event';
import { MessageUpdatedEvent } from '../events/message-updated.event';
import {
  CompletionBlock,
  CompletionBlockDO,
  CompletionBlockFactory,
  ContentCompletionBlock,
  ReasoningCompletionBlock,
  ToolCompletionBlock,
} from './completion-block.vo';

export type MessageDO = typeof messages.$inferSelect & {
  model: LLMs;
  role: MessageRoleEnum;
  status: MessageStatusEnum;
  content: string;
  context: MessageContext[];
  tools?: UseToolsDto;
  error?: RestErrorInfo;
  mode?: MessageModeEnum;
  command?: EditCommandDto;
  shortcut?: UserMessageType['shortcut'];
  traceId?: string;
  // Modern format - blocks-based
  blocks?: CompletionBlockDO[];
  // Legacy format - traditional fields
  reasoning?: string;
};

/**
 * 抽象消息类 - 定义所有消息的共同属性和行为
 */
export abstract class Message extends AggregateRoot {
  public readonly id: string;
  public readonly chatId: string;
  public readonly createdAt: Date;
  public updatedAt: Date;
  public deletedAt?: Date;
  public readonly role: MessageRoleEnum;
  public status: MessageStatusEnum;
  public model?: LLMs;
  public isModified = false;

  constructor(
    data: {
      id: string;
      chatId: string;
      createdAt: Date;
      updatedAt: Date;
      deletedAt?: Date;
      role: MessageRoleEnum;
      status: MessageStatusEnum;
      model?: LLMs;
    },
    public readonly isNew: boolean = false,
  ) {
    super();
    this.id = data.id;
    this.chatId = data.chatId;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
    this.deletedAt = data.deletedAt;
    this.role = data.role;
    this.status = data.status;
    this.model = data.model || DEFAULT_AI_CHAT_MODEL;
  }

  /**
   * 业务规则：检查消息是否已被删除
   */
  isDeleted(): boolean {
    return !!this.deletedAt;
  }

  /**
   * 业务规则：检查消息是否为用户消息
   */
  isUserMessage(): boolean {
    return this.role === MessageRoleEnum.USER;
  }

  /**
   * 业务规则：检查消息是否为助手消息
   */
  isAssistantMessage(): boolean {
    return this.role === MessageRoleEnum.ASSISTANT;
  }

  /**
   * 业务规则：检查消息是否已完成
   */
  isCompleted(): boolean {
    return this.status === MessageStatusEnum.DONE;
  }

  /**
   * 业务规则：检查消息是否正在生成
   */
  isGenerating(): boolean {
    return this.status === MessageStatusEnum.ING;
  }

  /**
   * 业务规则：检查消息是否出错
   */
  hasError(): boolean {
    return this.status === MessageStatusEnum.ERROR;
  }

  /**
   * 业务规则：检查消息是否被中止
   */
  isAborted(): boolean {
    return this.status === MessageStatusEnum.ABORT;
  }

  /**
   * 更新消息状态
   */
  updateStatus(newStatus: MessageStatusEnum): void {
    if (this.isDeleted()) {
      throw new Error('Cannot update status of deleted message');
    }

    const oldStatus = this.status;
    this.status = newStatus;
    this.updatedAt = new Date();

    // 发布更新事件
    this.apply(new MessageUpdatedEvent(this.id, this.chatId, oldStatus, newStatus));
  }

  /**
   * 删除消息
   * 执行软删除，并发布删除事件
   */
  delete(): void {
    if (this.isDeleted()) {
      throw new Error('Message is already deleted');
    }

    this.deletedAt = new Date();
    this.updatedAt = new Date();
    this.isModified = true;

    // 发布删除事件
    this.apply(new MessageDeletedEvent(this.id, this.chatId, this.deletedAt));
  }

  abstract toDO(): MessageDO;
  abstract getTextContent(): string;
  abstract addBlockDO(block: CompletionBlockDO): void;
  abstract toCompletionStreamChunk(): object;
}

/**
 * 用户消息类 - 处理用户发送的消息
 * 存储 UI 界面上的交互数据以及设置
 */
export class UserMessage extends Message {
  public tools: UseToolsDto;
  public command: EditCommandDto;
  public mode: MessageModeEnum;
  public boardId: string | null;
  public origin: ChatOriginDto;
  public content: string;
  public selection: string;
  public atReferences: AtReferenceDto[];
  public shortcut?: ShortcutDto;

  constructor(
    data: {
      id: string;
      chatId: string;
      createdAt: Date;
      updatedAt: Date;
      deletedAt?: Date;
      role: MessageRoleEnum;
      status: MessageStatusEnum;
      model?: LLMs;
      // additional params
      origin: ChatOriginDto;
      content: string;
      selection: string;
      atReferences: AtReferenceDto[];
      boardId: string;
      mode?: MessageModeEnum;
      tools?: UseToolsDto;
      command?: EditCommandDto;
      shortcut?: ShortcutDto;
    },
    public readonly isNew: boolean = false,
  ) {
    super({
      ...data,
      role: MessageRoleEnum.USER,
      status: MessageStatusEnum.DONE,
    });
    this.origin = data.origin;
    this.content = data.content || '';
    this.selection = data.selection || '';
    this.atReferences = data.atReferences || [];
    this.boardId =
      data.boardId || data.origin?.type === ChatOriginTypeEnum.BOARD
        ? (data.origin as ChatBoardOrigin)?.id
        : null;
    this.mode = data.mode || MessageModeEnum.ASK;
    this.tools = data.tools || {};
    this.command = data.command || undefined;
    this.shortcut = data.shortcut || undefined;
  }

  static fromDO(data: MessageDO): UserMessage {
    const origin = data.context?.find((c) => c.type === MessageContextEnum.ORIGIN)?.origin || {};
    const selection =
      data.context?.find((c) => c.type === MessageContextEnum.SELECTION)?.text || '';
    const atReferences =
      data.context?.find((c) => c.type === MessageContextEnum.AT)?.at_references || [];
    const boardId =
      data.context?.find((c) => c.type === MessageContextEnum.BOARD_ID)?.board_id || '';

    return new UserMessage(
      {
        ...data,
        origin: snakeToCamel(origin),
        content: data.content || '',
        selection,
        atReferences: snakeToCamel(atReferences),
        boardId,
        mode: data.mode ?? MessageModeEnum.ASK,
        tools: data.tools || {},
        command: data.command ?? undefined,
        shortcut: data.shortcut ?? undefined,
      },
      !!data.id,
    );
  }

  static createNew(data: {
    chatId: string;
    mode: MessageModeEnum;
    model?: LLMs;
    origin: ChatOriginDto;
    message: string;
    tools?: UseToolsDto;
    command?: EditCommandDto;
    boardId?: string;
    selection?: string;
    atReferences?: AtReferenceDto[];
    shortcut?: ShortcutDto;
  }): UserMessage {
    const {
      chatId,
      mode,
      model,
      origin,
      message,
      tools,
      command,
      boardId,
      selection,
      atReferences,
      shortcut,
    } = data;

    return new UserMessage({
      id: uuidv4(),
      chatId: chatId,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: MessageRoleEnum.USER,
      status: MessageStatusEnum.DONE,
      content: message,
      tools,
      mode,
      model,
      origin,
      selection,
      atReferences,
      boardId,
      command,
      shortcut,
    });
  }

  toDO(): MessageDO {
    const context: MessageContext[] = [
      {
        type: MessageContextEnum.ORIGIN,
        origin: this.origin,
      },
    ];
    if (this.selection) {
      context.push({
        type: MessageContextEnum.SELECTION,
        text: this.selection,
      });
    }
    if (this.atReferences.length) {
      context.push({
        type: MessageContextEnum.AT,
        at_references: camelToSnake(this.atReferences),
      });
    }
    if (this.boardId) {
      context.push({
        type: MessageContextEnum.BOARD_ID,
        board_id: this.boardId,
      });
    }

    return {
      id: this.id,
      chatId: this.chatId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      deletedAt: this.deletedAt,
      role: this.role,
      status: this.status,
      model: this.model,
      context,
      content: this.content,
      tools: this.tools,
      mode: this.mode,
      error: null,
      command: camelToSnake(this.command),
      shortcut: camelToSnake(this.shortcut),
      traceId: null,
      reasoning: null,
    };
  }

  toCompletionStreamChunk() {
    return camelToSnake({
      id: this.id,
      chatId: this.chatId,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      role: this.role,
      status: this.status,
      content: this.content,
      origin: this.origin,
      selection: this.selection,
      atReferences: this.atReferences || [],
      boardId: this.boardId,
      tools: this.tools,
      command: this.command,
      mode: this.mode,
      shortcut: this.shortcut,
    });
  }

  addBlockDO(_: CompletionBlockDO): void {
    throw new Error('User Message do not have Blocks');
  }

  getTextContent(): string {
    return this.content;
  }
}

/**
 * 助手消息类 - 处理AI助手生成的消息
 * 特点：支持 CompletionBlock，包含复杂的生成逻辑
 */
export class AssistantMessage extends Message {
  public blocks: CompletionBlock[];
  public traceId?: string;
  public error?: RestErrorInfo;
  public readonly mode?: MessageModeEnum;
  private readonly completionBlockFactory = new CompletionBlockFactory();

  constructor(data: {
    id: string;
    chatId: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
    role: MessageRoleEnum;
    status: MessageStatusEnum;
    model?: LLMs;
    // Assistant-specific properties
    mode?: MessageModeEnum;
    blocks?: CompletionBlock[];
    traceId?: string;
    error?: RestErrorInfo;
  }) {
    super({
      ...data,
      role: MessageRoleEnum.ASSISTANT,
    });
    this.blocks = data.blocks || [];
    this.traceId = data.traceId;
    this.error = data.error;
    this.mode = data.mode;
  }

  /**
   * 设置错误信息
   */
  setError(error: RestErrorInfo): void {
    if (this.isDeleted()) {
      throw new Error('Cannot set error on deleted message');
    }

    this.error = error;
    this.status = MessageStatusEnum.ERROR;
    this.updatedAt = new Date();
    this.isModified = true;

    // 发布更新事件
    this.apply(new MessageUpdatedEvent(this.id, this.chatId, 'error_set', error));
  }

  /**
   * 设置追踪ID
   */
  setTraceId(traceId: string): void {
    this.traceId = traceId;
    this.updatedAt = new Date();
    this.isModified = true;
  }

  /**
   * 设置挂载 blocks
   */
  setBlocks(blocks: CompletionBlock[]): void {
    this.blocks = blocks;
  }

  addBlockDO(block: CompletionBlockDO): void {
    this.blocks.push(this.completionBlockFactory.fromDO(block));
  }

  // ========== CompletionBlock 相关方法 ==========

  /**
   * 获取所有完成块
   */
  getBlocks(): CompletionBlock[] {
    return [...this.blocks];
  }

  /**
   * 根据类型获取完成块
   */
  getBlocksByType(type: CompletionBlockTypeEnum): CompletionBlock[] {
    return this.blocks.filter((block) => block.type === type);
  }

  /**
   * 根据ID获取完成块
   */
  getBlockById(blockId: string): CompletionBlock | undefined {
    return this.blocks.find((block) => block.id === blockId);
  }

  /**
   * 添加完成块
   */
  addBlock(block: CompletionBlock): void {
    if (this.isDeleted()) {
      throw new Error('Cannot add block to deleted message');
    }

    // 验证块属于此消息
    if (block.messageId !== this.id) {
      throw new Error('Block does not belong to this message');
    }

    this.blocks.push(block);
    this.updatedAt = new Date();
    this.isModified = true;
  }

  /**
   * 创建并添加内容完成块
   */
  addContentBlock(blockId: string, content?: string): ContentCompletionBlock {
    const block = ContentCompletionBlock.createNew({
      messageId: this.id,
      data: content,
    });

    this.addBlock(block);

    return block;
  }

  /**
   * 创建并添加推理完成块
   */
  addReasoningBlock(blockId: string, reasoning?: string): ReasoningCompletionBlock {
    const block = ReasoningCompletionBlock.createNew({
      messageId: this.id,
      data: reasoning,
    });

    this.addBlock(block);

    return block;
  }

  /**
   * 创建并添加工具完成块
   */
  addToolBlock(
    blockId: string,
    toolId: string,
    toolName: string,
    toolArguments?: Record<string, unknown>,
  ): ToolCompletionBlock {
    const block = ToolCompletionBlock.createNew({
      messageId: this.id,
      toolId: toolId,
      toolName: toolName,
      toolArguments: toolArguments,
    });

    this.addBlock(block);

    return block;
  }

  /**
   * 移除完成块
   */
  removeBlock(blockId: string): void {
    const index = this.blocks.findIndex((block) => block.id === blockId);
    if (index !== -1) {
      this.blocks.splice(index, 1);
      this.updatedAt = new Date();
      this.isModified = true;
    }
  }

  /**
   * 获取消息的推理内容（来自推理块）
   */
  getReasoningContent(): string {
    const reasoningBlocks = this.getBlocksByType(CompletionBlockTypeEnum.REASONING);
    return reasoningBlocks.map((block) => block.data).join('\n');
  }

  /**
   * 获取所有工具调用块
   */
  getToolBlocks(): ToolCompletionBlock[] {
    return this.getBlocksByType(CompletionBlockTypeEnum.TOOL) as ToolCompletionBlock[];
  }

  /**
   * 检查是否有正在生成的块
   */
  hasGeneratingBlocks(): boolean {
    return this.blocks.some((block) => block.isGenerating());
  }

  /**
   * 检查是否有错误的块
   */
  hasErrorBlocks(): boolean {
    return this.blocks.some((block) => block.hasError());
  }

  /**
   * 更新完成块并发布事件
   */
  updateBlock(blockId: string, updateFn: (block: CompletionBlock) => void): void {
    const block = this.getBlockById(blockId);
    if (!block) {
      throw new Error(`Block not found: ${blockId}`);
    }

    updateFn(block);
    this.updatedAt = new Date();
    this.isModified = true;
  }

  getTextContent(): string {
    return this.blocks
      .map((block) => {
        if (block.type === CompletionBlockTypeEnum.TOOL) {
          return '';
        } else if (block.type === CompletionBlockTypeEnum.REASONING) {
          return `<think>\n${block.data}\n</think>`;
        } else if (block.type === CompletionBlockTypeEnum.CONTENT) {
          return block.data;
        }
      })
      .join('\n\n');
  }

  /**
   * 从数据对象创建助手消息
   */
  static fromDO(data: MessageDO): AssistantMessage {
    const blocks = [];
    if (data.blocks) {
      const factory = new CompletionBlockFactory();
      data.blocks.forEach((block) => {
        blocks.push(factory.fromDO(block));
      });
    }
    return new AssistantMessage({
      id: data.id,
      chatId: data.chatId,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      role: MessageRoleEnum.ASSISTANT,
      status: data.status,
      model: data.model,
      blocks,
      traceId: data.traceId,
      error: data.error,
    });
  }

  /**
   * 创建新的助手消息
   */
  static createNew(data: { chatId: string; model?: LLMs; content?: string }): AssistantMessage {
    const { chatId, model, content } = data;

    const message = new AssistantMessage({
      id: uuidv4(),
      chatId: chatId,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: MessageRoleEnum.ASSISTANT,
      status: content ? MessageStatusEnum.DONE : MessageStatusEnum.QUEUED,
      model: model || DEFAULT_AI_CHAT_MODEL,
      blocks: [],
    });
    if (content) {
      message.addContentBlock(uuidv4(), content);
    }
    return message;
  }

  /**
   * 创建新的助手消息
   */
  static createNewFromUserMessage(userMessage: UserMessage): AssistantMessage {
    return new AssistantMessage({
      id: uuidv4(),
      chatId: userMessage.chatId,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: MessageRoleEnum.ASSISTANT,
      status: MessageStatusEnum.QUEUED,
      mode: userMessage.mode,
      model: userMessage.model || DEFAULT_AI_CHAT_MODEL,
      blocks: [],
    });
  }

  /**
   * 转换为数据对象
   */
  toDO(): MessageDO {
    return {
      id: this.id,
      chatId: this.chatId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      deletedAt: this.deletedAt,
      role: this.role,
      status: this.status,
      model: this.model,
      context: [], // Assistant messages don't have context like user messages
      content: '',
      tools: {}, // Assistant messages don't have tools
      error: this.error,
      mode: this.mode, // Assistant messages don't have mode
      command: undefined, // Assistant messages don't have command
      shortcut: null, // Assistant messages don't have shortcut
      traceId: this.traceId,
      reasoning: '',
    };
  }

  toCompletionStreamChunk() {
    return camelToSnake({
      id: this.id,
      chatId: this.chatId,
      createdAt: this.createdAt.toString(),
      updatedAt: this.updatedAt.toString(),
      role: this.role,
      status: this.status,
      mode: this.mode,
      traceId: this.traceId,
      blocks: [],
    });
  }
}
