/**
 * Chat Assistant V2 Controller - V2 聊天助手控制器
 * 处理 V2 API 聊天助手相关的HTTP请求
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v2/chatAssistant/getChatDetail/route.ts
 */

import { Body, Controller, HttpCode, Post, Sse } from '@nestjs/common';
import { ApiBody, ApiExtraModels, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ChatModeEnum, DEFAULT_AI_AGENT_MODEL, MessageModeEnum } from '@/common/types';
import { observableToSse } from '@/modules/ai/utils/toObservable';
import { BaseController } from '@/shared/base.controller';
import { ChatIdDto, ChatOriginDto } from '../dto/chat.dto';
import {
  CompletionBlockDto,
  CompletionChatDto,
  CompletionMessageDto,
  CompletionStreamAppendJsonChunkDto,
  CompletionStreamAppendStringChunkDto,
  CompletionStreamEventDto,
  CompletionStreamInsertChunkDto,
  CompletionStreamReplaceChunkDto,
} from '../dto/completion-stream.dto';
import {
  ChatDetailV2Dto,
  CreateChatDto,
  CreateEmptyChatDto,
  ListChatHistoryV2Dto,
  ListChatHistoryV2ResponseDto,
  RegenerateMessageV2Dto,
  SendMessageDto,
} from '../dto/v2/chat-v2.dto';
import { CreateChatCommand } from '../services/commands/create-chat.command';
import { CreateEmptyChatCommand } from '../services/commands/create-empty-chat.command';
import { DeleteChatCommand } from '../services/commands/delete-chat.command';
import { RegenerateMessageCommand } from '../services/commands/regenerate-message.command';
import { SendMessageCommand } from '../services/commands/send-message.command';
import { GetChatDetailQuery } from '../services/queries/get-chat-detail.query';
import { GetChatDetailByOriginQuery } from '../services/queries/get-chat-detail-by-origin.query';
import { ListChatHistoryQuery } from '../services/queries/list-chat-history.query';

const SSE_API_RESPONSE = {
  'text/event-stream': {
    schema: {
      type: 'object',
      oneOf: [
        { $ref: '#/components/schemas/CompletionStreamEventDto' },
        { $ref: '#/components/schemas/CompletionStreamInsertChunkDto' },
        { $ref: '#/components/schemas/CompletionStreamReplaceChunkDto' },
        { $ref: '#/components/schemas/CompletionStreamAppendStringChunkDto' },
        { $ref: '#/components/schemas/CompletionStreamAppendJsonChunkDto' },
      ],
    },
  },
};

@ApiTags('Chat V2')
@ApiExtraModels(
  CompletionStreamEventDto,
  CompletionStreamInsertChunkDto,
  CompletionStreamReplaceChunkDto,
  CompletionStreamAppendStringChunkDto,
  CompletionStreamAppendJsonChunkDto,
  CompletionChatDto,
  CompletionMessageDto,
  CompletionBlockDto,
)
@Controller('api/v2/chatAssistant')
export class ChatV2Controller extends BaseController {
  @Post('createEmptyChat')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Create empty chat for assistant',
    description:
      'Creates a new empty chat without any messages, suitable for assistant initialization',
  })
  @ApiBody({ type: CreateEmptyChatDto })
  @ApiResponse({
    status: 200,
    description: 'Empty chat created successfully',
    type: ChatDetailV2Dto,
  })
  async createEmptyChat(@Body() dto: CreateEmptyChatDto): Promise<ChatDetailV2Dto> {
    const command = new CreateEmptyChatCommand(this.getUserId(), dto);
    return await this.commandBus.execute(command);
  }

  @Post('createChat')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Create chat and send message',
    description:
      'Creates a new chat and sends a message, returning a server-sent event stream with completion chunks',
  })
  @ApiBody({ type: CreateChatDto })
  @ApiResponse({
    status: 200,
    content: SSE_API_RESPONSE,
  })
  @Sse()
  async createChat(@Body() dto: CreateChatDto) {
    const command = new CreateChatCommand({
      userId: this.getUserId(),
      boardId: dto.boardId,
      origin: dto.origin,
      version: 'v2',
      message: dto.message,
      messageMode: dto.messageMode,
      mode: ChatModeEnum.CHAT,
      chatModel: dto.chatModel,
      selection: dto.selection,
      tools: dto.tools,
      command: dto.command,
      atReferences: dto.atReferences,
    });
    const observable = await this.commandBus.execute(command);
    return observableToSse(observable);
  }

  @Post('getChatDetail')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Get chat details (V2 format)',
  })
  @ApiBody({ type: ChatIdDto })
  @ApiResponse({
    status: 200,
    description: 'Chat details retrieved successfully',
    type: ChatDetailV2Dto,
  })
  async getChatDetail(@Body() dto: ChatIdDto): Promise<ChatDetailV2Dto> {
    // 使用 v2 版本的查询来获取现代格式的聊天详情
    const query = new GetChatDetailQuery(dto.chatId, 'v2');
    return await this.queryBus.execute(query);
  }

  @Post('getChatDetailByOrigin')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Get chat details by origin (V2 format)',
    description: '根据来源查询聊天详情，返回最相关的单个聊天详情，用于Chat Assistant默认展示',
  })
  @ApiBody({ type: ChatOriginDto })
  @ApiResponse({
    status: 200,
    type: ChatDetailV2Dto,
  })
  async getChatDetailByOrigin(@Body() origin: ChatOriginDto): Promise<ChatDetailV2Dto | null> {
    // 根据来源查询单个最相关的聊天详情，使用V2格式
    const query = new GetChatDetailByOriginQuery(origin, this.getUserId(), 'v2');
    return await this.queryBus.execute(query);
  }

  @Post('listChatHistory')
  @HttpCode(200)
  @ApiOperation({
    summary: 'List chat history (V2 format)',
  })
  @ApiBody({ type: ListChatHistoryV2Dto })
  @ApiResponse({
    status: 200,
    type: ListChatHistoryV2ResponseDto,
  })
  async listChatHistory(@Body() dto: ListChatHistoryV2Dto): Promise<ListChatHistoryV2ResponseDto> {
    const query = new ListChatHistoryQuery(
      this.getUserId(),
      dto.current || 0,
      dto.pageSize || 10,
      dto.origin,
      dto.query,
    );
    return await this.queryBus.execute(query);
  }

  @Post('sendMessage')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Send a message to an existing chat',
    description:
      'Sends a message to an existing chat and returns a server-sent event stream with AI response chunks',
  })
  @ApiBody({ type: SendMessageDto })
  @ApiResponse({
    status: 200,
    content: SSE_API_RESPONSE,
  })
  @Sse()
  async sendMessage(@Body() dto: SendMessageDto) {
    const command = new SendMessageCommand({
      userId: this.getUserId(),
      chatId: dto.chatId,
      message: dto.message,
      selection: dto.selection,
      atReferences: dto.atReferences,
      messageMode: dto.messageMode,
      tools: dto.tools,
      shortcut: dto.shortcut,
      command: dto.command,
      chatModel: dto.messageMode === MessageModeEnum.AGENT ? DEFAULT_AI_AGENT_MODEL : dto.chatModel,
    });
    const observable = await this.commandBus.execute(command);
    return observableToSse(observable);
  }

  @Post('regenerateMessage')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Regenerate message (V2 format)',
    description:
      'Regenerates an AI assistant message and returns a server-sent event stream with the new response',
  })
  @ApiBody({ type: RegenerateMessageV2Dto })
  @ApiResponse({
    status: 200,
    content: SSE_API_RESPONSE,
  })
  @Sse()
  async regenerateMessage(@Body() dto: RegenerateMessageV2Dto) {
    const command = new RegenerateMessageCommand(
      this.getUserId(),
      dto.chatId,
      dto.userMessageId,
      'v2',
      undefined,
      undefined,
    );
    const observable = await this.commandBus.execute(command);
    return observableToSse(observable);
  }

  @Post('deleteChat')
  @HttpCode(200)
  @ApiOperation({ summary: 'Delete Chat' })
  @ApiBody({ type: ChatIdDto })
  @ApiResponse({ status: 200, description: 'Chat deleted successfully' })
  async deleteChat(@Body() dto: ChatIdDto) {
    const command = new DeleteChatCommand(dto.chatId);
    return await this.commandBus.execute(command);
  }
}
